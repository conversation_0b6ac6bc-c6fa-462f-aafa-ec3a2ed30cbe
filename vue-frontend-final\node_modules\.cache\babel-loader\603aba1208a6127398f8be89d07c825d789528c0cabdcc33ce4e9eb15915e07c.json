{"ast": null, "code": "import { createApp } from 'vue';\nimport { createPinia } from 'pinia';\nimport App from './App.vue';\nimport router from './router';\n\n// Import Font Awesome\nimport '@fortawesome/fontawesome-free/css/all.css';\nconst app = createApp(App);\napp.use(createPinia());\napp.use(router);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "createPinia", "App", "router", "app", "use", "mount"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/new/vue-frontend-project/vue-frontend-final/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport { createPinia } from 'pinia'\nimport App from './App.vue'\nimport router from './router'\n\n// Import Font Awesome\nimport '@fortawesome/fontawesome-free/css/all.css'\n\nconst app = createApp(App)\n\napp.use(createPinia())\napp.use(router)\n\napp.mount('#app')\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,SAASC,WAAW,QAAQ,OAAO;AACnC,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AACA,OAAO,2CAA2C;AAElD,MAAMC,GAAG,GAAGJ,SAAS,CAACE,GAAG,CAAC;AAE1BE,GAAG,CAACC,GAAG,CAACJ,WAAW,CAAC,CAAC,CAAC;AACtBG,GAAG,CAACC,GAAG,CAACF,MAAM,CAAC;AAEfC,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}