{"ast": null, "code": "export default {\n  name: 'App'\n};", "map": {"version": 3, "names": ["name"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  direction: rtl;\n  text-align: right;\n}\n\n#app {\n  min-height: 100vh;\n}\n</style>\n"], "mappings": "AAOA,eAAe;EACbA,IAAI,EAAE;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}