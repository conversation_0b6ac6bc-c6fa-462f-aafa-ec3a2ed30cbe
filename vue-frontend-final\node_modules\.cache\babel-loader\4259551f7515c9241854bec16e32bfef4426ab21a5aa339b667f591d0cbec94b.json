{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useAuthStore } from '../stores/auth';\nexport default {\n  name: 'LoginView',\n  setup() {\n    const router = useRouter();\n    const authStore = useAuthStore();\n\n    // Reactive data\n    const credentials = reactive({\n      username: '',\n      password: ''\n    });\n    const showPassword = ref(false);\n    const rememberMe = ref(false);\n    const successMessage = ref('');\n    const systemStatus = ref({\n      text: 'النظام متصل',\n      class: 'online'\n    });\n\n    // Methods\n    const togglePassword = () => {\n      showPassword.value = !showPassword.value;\n    };\n    const fillDemoCredentials = () => {\n      credentials.username = 'Ahmed';\n      credentials.password = 'Ahmed123!';\n    };\n    const handleLogin = async () => {\n      try {\n        const response = await authStore.login(credentials);\n        if (response.success) {\n          successMessage.value = `مرحباً ${response.user.fullName || response.user.username}!`;\n\n          // Remember user if checkbox is checked\n          if (rememberMe.value) {\n            localStorage.setItem('rememberUser', credentials.username);\n          } else {\n            localStorage.removeItem('rememberUser');\n          }\n\n          // Redirect to dashboard after short delay\n          setTimeout(() => {\n            router.push('/dashboard');\n          }, 1500);\n        }\n      } catch (error) {\n        console.error('Login error:', error);\n      }\n    };\n    const checkSystemStatus = () => {\n      // محاكاة فحص حالة النظام\n      systemStatus.value = {\n        text: 'النظام متصل',\n        class: 'online'\n      };\n    };\n\n    // Lifecycle\n    onMounted(() => {\n      checkSystemStatus();\n\n      // Load remembered username\n      const rememberedUser = localStorage.getItem('rememberUser');\n      if (rememberedUser) {\n        credentials.username = rememberedUser;\n        rememberMe.value = true;\n      }\n    });\n    return {\n      credentials,\n      showPassword,\n      rememberMe,\n      successMessage,\n      systemStatus,\n      authStore,\n      togglePassword,\n      fillDemoCredentials,\n      handleLogin\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "useRouter", "useAuthStore", "name", "setup", "router", "authStore", "credentials", "username", "password", "showPassword", "rememberMe", "successMessage", "systemStatus", "text", "class", "togglePassword", "value", "fillDemoCredentials", "handleLogin", "response", "login", "success", "user", "fullName", "localStorage", "setItem", "removeItem", "setTimeout", "push", "error", "console", "checkSystemStatus", "<PERSON><PERSON><PERSON>", "getItem"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\views\\LoginView.vue"], "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <div class=\"login-container\">\n      <div class=\"login-card\">\n        <!-- Header -->\n        <div class=\"login-header\">\n          <div class=\"logo\">\n            <i class=\"fas fa-store\"></i>\n            <h1>متجر الإلكترونيات</h1>\n          </div>\n          <p class=\"subtitle\">نظام إدارة المتجر</p>\n        </div>\n\n        <!-- Login Form -->\n        <form @submit.prevent=\"handleLogin\" class=\"login-form\">\n          <div class=\"form-group\">\n            <label for=\"username\">\n              <i class=\"fas fa-user\"></i>\n              اسم المستخدم\n            </label>\n            <input \n              type=\"text\" \n              id=\"username\" \n              v-model=\"credentials.username\"\n              required \n              placeholder=\"أدخل اسم المستخدم\"\n              autocomplete=\"username\"\n              :disabled=\"authStore.isLoading\"\n            >\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"password\">\n              <i class=\"fas fa-lock\"></i>\n              كلمة المرور\n            </label>\n            <div class=\"password-input\">\n              <input \n                :type=\"showPassword ? 'text' : 'password'\" \n                id=\"password\" \n                v-model=\"credentials.password\"\n                required \n                placeholder=\"أدخل كلمة المرور\"\n                autocomplete=\"current-password\"\n                :disabled=\"authStore.isLoading\"\n              >\n              <button type=\"button\" class=\"toggle-password\" @click=\"togglePassword\">\n                <i :class=\"showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'\"></i>\n              </button>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label class=\"checkbox-container\">\n              <input type=\"checkbox\" v-model=\"rememberMe\" :disabled=\"authStore.isLoading\">\n              <span class=\"checkmark\"></span>\n              تذكرني\n            </label>\n          </div>\n\n          <button type=\"submit\" class=\"login-btn\" :disabled=\"authStore.isLoading\">\n            <i v-if=\"!authStore.isLoading\" class=\"fas fa-sign-in-alt\"></i>\n            <i v-else class=\"fas fa-spinner fa-spin\"></i>\n            {{ authStore.isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول' }}\n          </button>\n        </form>\n\n        <!-- Error Message -->\n        <div v-if=\"authStore.error\" class=\"error-message\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <span>{{ authStore.error }}</span>\n        </div>\n\n        <!-- Success Message -->\n        <div v-if=\"successMessage\" class=\"success-message\">\n          <i class=\"fas fa-check-circle\"></i>\n          <span>{{ successMessage }}</span>\n        </div>\n\n        <!-- Demo Credentials -->\n        <div class=\"demo-credentials\">\n          <h3><i class=\"fas fa-info-circle\"></i> بيانات تجريبية</h3>\n          <div class=\"demo-user\">\n            <strong>Super Admin:</strong>\n            <br>\n            <span>المستخدم: Ahmed</span>\n            <br>\n            <span>كلمة المرور: Ahmed123!</span>\n            <button type=\"button\" @click=\"fillDemoCredentials\" class=\"demo-btn\">\n              <i class=\"fas fa-magic\"></i>\n              استخدام البيانات التجريبية\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Footer -->\n      <div class=\"login-footer\">\n        <p>&copy; 2025 نظام إدارة المتجر الإلكتروني. جميع الحقوق محفوظة.</p>\n        <div class=\"system-status\">\n          <span class=\"status-indicator\">\n            <i class=\"fas fa-circle\" :class=\"systemStatus.class\"></i>\n            {{ systemStatus.text }}\n          </span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '../stores/auth'\n\nexport default {\n  name: 'LoginView',\n  setup() {\n    const router = useRouter()\n    const authStore = useAuthStore()\n    \n    // Reactive data\n    const credentials = reactive({\n      username: '',\n      password: ''\n    })\n    \n    const showPassword = ref(false)\n    const rememberMe = ref(false)\n    const successMessage = ref('')\n    const systemStatus = ref({\n      text: 'النظام متصل',\n      class: 'online'\n    })\n\n    // Methods\n    const togglePassword = () => {\n      showPassword.value = !showPassword.value\n    }\n\n    const fillDemoCredentials = () => {\n      credentials.username = 'Ahmed'\n      credentials.password = 'Ahmed123!'\n    }\n\n    const handleLogin = async () => {\n      try {\n        const response = await authStore.login(credentials)\n        \n        if (response.success) {\n          successMessage.value = `مرحباً ${response.user.fullName || response.user.username}!`\n          \n          // Remember user if checkbox is checked\n          if (rememberMe.value) {\n            localStorage.setItem('rememberUser', credentials.username)\n          } else {\n            localStorage.removeItem('rememberUser')\n          }\n          \n          // Redirect to dashboard after short delay\n          setTimeout(() => {\n            router.push('/dashboard')\n          }, 1500)\n        }\n      } catch (error) {\n        console.error('Login error:', error)\n      }\n    }\n\n    const checkSystemStatus = () => {\n      // محاكاة فحص حالة النظام\n      systemStatus.value = {\n        text: 'النظام متصل',\n        class: 'online'\n      }\n    }\n\n    // Lifecycle\n    onMounted(() => {\n      checkSystemStatus()\n      \n      // Load remembered username\n      const rememberedUser = localStorage.getItem('rememberUser')\n      if (rememberedUser) {\n        credentials.username = rememberedUser\n        rememberMe.value = true\n      }\n    })\n\n    return {\n      credentials,\n      showPassword,\n      rememberMe,\n      successMessage,\n      systemStatus,\n      authStore,\n      togglePassword,\n      fillDemoCredentials,\n      handleLogin\n    }\n  }\n}\n</script>\n\n<style scoped>\n/* ===== Global Styles ===== */\n.login-page {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  min-height: 100vh;\n  direction: rtl;\n  text-align: right;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.login-container {\n  width: 100%;\n  max-width: 450px;\n  animation: fadeInUp 0.6s ease-out;\n}\n\n.login-card {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n/* ===== Header Styles ===== */\n.login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n  margin-bottom: 10px;\n}\n\n.logo i {\n  font-size: 2.5rem;\n  color: #667eea;\n}\n\n.logo h1 {\n  font-size: 1.8rem;\n  color: #333;\n  font-weight: 700;\n  margin: 0;\n}\n\n.subtitle {\n  color: #666;\n  font-size: 1rem;\n  margin-top: 5px;\n}\n\n/* ===== Form Styles ===== */\n.login-form {\n  margin-bottom: 20px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group label {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  color: #333;\n  font-weight: 500;\n  font-size: 0.95rem;\n}\n\n.form-group label i {\n  color: #667eea;\n  width: 16px;\n}\n\n.form-group input[type=\"text\"],\n.form-group input[type=\"password\"] {\n  width: 100%;\n  padding: 12px 16px;\n  border: 2px solid #e1e5e9;\n  border-radius: 10px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background: #fff;\n}\n\n.form-group input:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-group input:disabled {\n  background-color: #f8f9fa;\n  cursor: not-allowed;\n}\n\n.password-input {\n  position: relative;\n}\n\n.toggle-password {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #667eea;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 5px;\n  transition: background-color 0.3s ease;\n}\n\n.toggle-password:hover {\n  background-color: rgba(102, 126, 234, 0.1);\n}\n\n/* ===== Checkbox Styles ===== */\n.checkbox-container {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  cursor: pointer;\n  user-select: none;\n}\n\n.checkbox-container input[type=\"checkbox\"] {\n  width: auto;\n  margin: 0;\n}\n\n/* ===== Button Styles ===== */\n.login-btn {\n  width: 100%;\n  padding: 14px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 10px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n}\n\n.login-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n}\n\n.login-btn:disabled {\n  opacity: 0.7;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* ===== Message Styles ===== */\n.error-message,\n.success-message {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 12px 16px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  font-weight: 500;\n}\n\n.error-message {\n  background-color: #fee;\n  color: #c53030;\n  border: 1px solid #fed7d7;\n}\n\n.success-message {\n  background-color: #f0fff4;\n  color: #38a169;\n  border: 1px solid #c6f6d5;\n}\n\n/* ===== Demo Credentials ===== */\n.demo-credentials {\n  background: #f8f9fa;\n  border-radius: 10px;\n  padding: 20px;\n  margin-top: 20px;\n  text-align: center;\n}\n\n.demo-credentials h3 {\n  color: #495057;\n  margin-bottom: 15px;\n  font-size: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n}\n\n.demo-user {\n  background: white;\n  padding: 15px;\n  border-radius: 8px;\n  margin-bottom: 10px;\n}\n\n.demo-btn {\n  background: #28a745;\n  color: white;\n  border: none;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  margin-top: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  margin: 10px auto 0;\n}\n\n.demo-btn:hover {\n  background: #218838;\n}\n\n/* ===== Footer Styles ===== */\n.login-footer {\n  text-align: center;\n  margin-top: 30px;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.9rem;\n}\n\n.system-status {\n  margin-top: 10px;\n}\n\n.status-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n}\n\n.status-indicator.online i {\n  color: #28a745;\n}\n\n.status-indicator.offline i {\n  color: #dc3545;\n}\n\n/* ===== Animations ===== */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.fa-spin {\n  animation: spin 1s linear infinite;\n}\n</style>\n\n"], "mappings": ";AA+GA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,YAAW,QAAS,gBAAe;AAE5C,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIJ,SAAS,CAAC;IACzB,MAAMK,SAAQ,GAAIJ,YAAY,CAAC;;IAE/B;IACA,MAAMK,WAAU,GAAIR,QAAQ,CAAC;MAC3BS,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,YAAW,GAAIZ,GAAG,CAAC,KAAK;IAC9B,MAAMa,UAAS,GAAIb,GAAG,CAAC,KAAK;IAC5B,MAAMc,cAAa,GAAId,GAAG,CAAC,EAAE;IAC7B,MAAMe,YAAW,GAAIf,GAAG,CAAC;MACvBgB,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE;IACT,CAAC;;IAED;IACA,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3BN,YAAY,CAACO,KAAI,GAAI,CAACP,YAAY,CAACO,KAAI;IACzC;IAEA,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChCX,WAAW,CAACC,QAAO,GAAI,OAAM;MAC7BD,WAAW,CAACE,QAAO,GAAI,WAAU;IACnC;IAEA,MAAMU,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMd,SAAS,CAACe,KAAK,CAACd,WAAW;QAElD,IAAIa,QAAQ,CAACE,OAAO,EAAE;UACpBV,cAAc,CAACK,KAAI,GAAI,UAAUG,QAAQ,CAACG,IAAI,CAACC,QAAO,IAAKJ,QAAQ,CAACG,IAAI,CAACf,QAAQ,GAAE;;UAEnF;UACA,IAAIG,UAAU,CAACM,KAAK,EAAE;YACpBQ,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEnB,WAAW,CAACC,QAAQ;UAC3D,OAAO;YACLiB,YAAY,CAACE,UAAU,CAAC,cAAc;UACxC;;UAEA;UACAC,UAAU,CAAC,MAAM;YACfvB,MAAM,CAACwB,IAAI,CAAC,YAAY;UAC1B,CAAC,EAAE,IAAI;QACT;MACF,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK;MACrC;IACF;IAEA,MAAME,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACAnB,YAAY,CAACI,KAAI,GAAI;QACnBH,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE;MACT;IACF;;IAEA;IACAf,SAAS,CAAC,MAAM;MACdgC,iBAAiB,CAAC;;MAElB;MACA,MAAMC,cAAa,GAAIR,YAAY,CAACS,OAAO,CAAC,cAAc;MAC1D,IAAID,cAAc,EAAE;QAClB1B,WAAW,CAACC,QAAO,GAAIyB,cAAa;QACpCtB,UAAU,CAACM,KAAI,GAAI,IAAG;MACxB;IACF,CAAC;IAED,OAAO;MACLV,WAAW;MACXG,YAAY;MACZC,UAAU;MACVC,cAAc;MACdC,YAAY;MACZP,SAAS;MACTU,cAAc;MACdE,mBAAmB;MACnBC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}