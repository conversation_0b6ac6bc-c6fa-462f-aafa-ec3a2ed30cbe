{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"recent-activities\"\n};\nconst _hoisted_2 = {\n  class: \"activities-list\"\n};\nconst _hoisted_3 = {\n  class: \"activity-icon\"\n};\nconst _hoisted_4 = {\n  class: \"activity-content\"\n};\nconst _hoisted_5 = {\n  class: \"activity-text\"\n};\nconst _hoisted_6 = {\n  class: \"activity-time\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[0] || (_cache[0] = _createElementVNode(\"h2\", null, \"الأنشطة الأخيرة\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.activities, activity => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: activity.id,\n      class: \"activity-item\"\n    }, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"i\", {\n      class: _normalizeClass(activity.icon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"p\", _hoisted_5, _toDisplayString(activity.text), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_6, _toDisplayString(activity.time), 1 /* TEXT */)])]);\n  }), 128 /* KEYED_FRAGMENT */))])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$props", "activities", "activity", "key", "id", "_hoisted_3", "_normalizeClass", "icon", "_hoisted_4", "_hoisted_5", "_toDisplayString", "text", "_hoisted_6", "time"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\RecentActivities.vue"], "sourcesContent": ["<template>\n  <div class=\"recent-activities\">\n    <h2>الأنشطة الأخيرة</h2>\n    <div class=\"activities-list\">\n      <div v-for=\"activity in activities\" :key=\"activity.id\" class=\"activity-item\">\n        <div class=\"activity-icon\">\n          <i :class=\"activity.icon\"></i>\n        </div>\n        <div class=\"activity-content\">\n          <p class=\"activity-text\">{{ activity.text }}</p>\n          <span class=\"activity-time\">{{ activity.time }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RecentActivities',\n  props: {\n    activities: {\n      type: Array,\n      required: true\n    }\n  }\n}\n</script>\n\n<style scoped>\n.recent-activities {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.recent-activities h2 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.activities-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.activity-item {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background: #f8fafc;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.activity-item:hover {\n  background: #f1f5f9;\n  transform: translateX(-2px);\n}\n\n.activity-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.activity-content {\n  flex: 1;\n}\n\n.activity-text {\n  font-weight: 500;\n  color: #334155;\n  margin: 0 0 4px 0;\n}\n\n.activity-time {\n  font-size: 0.75rem;\n  color: #64748b;\n}\n</style>\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAiB;;EAEnBA,KAAK,EAAC;AAAe;;EAGrBA,KAAK,EAAC;AAAkB;;EACxBA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAe;;uBATnCC,mBAAA,CAaM,OAbNC,UAaM,G,0BAZJC,mBAAA,CAAwB,YAApB,iBAAe,qBACnBA,mBAAA,CAUM,OAVNC,UAUM,I,kBATJH,mBAAA,CAQMI,SAAA,QAAAC,WAAA,CARkBC,MAAA,CAAAC,UAAU,EAAtBC,QAAQ;yBAApBR,mBAAA,CAQM;MAR+BS,GAAG,EAAED,QAAQ,CAACE,EAAE;MAAEX,KAAK,EAAC;QAC3DG,mBAAA,CAEM,OAFNS,UAEM,GADJT,mBAAA,CAA8B;MAA1BH,KAAK,EAAAa,eAAA,CAAEJ,QAAQ,CAACK,IAAI;+BAE1BX,mBAAA,CAGM,OAHNY,UAGM,GAFJZ,mBAAA,CAAgD,KAAhDa,UAAgD,EAAAC,gBAAA,CAApBR,QAAQ,CAACS,IAAI,kBACzCf,mBAAA,CAAsD,QAAtDgB,UAAsD,EAAAF,gBAAA,CAAvBR,QAAQ,CAACW,IAAI,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}