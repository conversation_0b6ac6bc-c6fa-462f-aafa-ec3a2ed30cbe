{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\n/*!\n * pinia v3.0.3\n * (c) 2025 <PERSON> Morote\n * @license MIT\n */\nimport { hasInjectionContext, inject, toRaw, watch, unref, markRaw, effectScope, ref, isRef, isReactive, getCurrentScope, onScopeDispose, getCurrentInstance, reactive, toRef, nextTick, computed, toRefs } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * setActivePinia must be called to handle SSR at the top of functions like\n * `fetch`, `setup`, `serverPrefetch` and others\n */\nlet activePinia;\n/**\n * Sets or unsets the active pinia. Used in SSR and internally when calling\n * actions and getters\n *\n * @param pinia - Pinia instance\n */\n// @ts-expect-error: cannot constrain the type of the return\nconst setActivePinia = pinia => activePinia = pinia;\n/**\n * Get the currently active pinia if there is any.\n */\nconst getActivePinia = () => hasInjectionContext() && inject(piniaSymbol) || activePinia;\nconst piniaSymbol = process.env.NODE_ENV !== 'production' ? Symbol('pinia') : /* istanbul ignore next */Symbol();\nfunction isPlainObject(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no) {\n  return o && typeof o === 'object' && Object.prototype.toString.call(o) === '[object Object]' && typeof o.toJSON !== 'function';\n}\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\n// TODO: can we change these to numbers?\n/**\n * Possible types for SubscriptionCallback\n */\nvar MutationType;\n(function (MutationType) {\n  /**\n   * Direct mutation of the state:\n   *\n   * - `store.name = 'new name'`\n   * - `store.$state.name = 'new name'`\n   * - `store.list.push('new item')`\n   */\n  MutationType[\"direct\"] = \"direct\";\n  /**\n   * Mutated the state with `$patch` and an object\n   *\n   * - `store.$patch({ name: 'newName' })`\n   */\n  MutationType[\"patchObject\"] = \"patch object\";\n  /**\n   * Mutated the state with `$patch` and a function\n   *\n   * - `store.$patch(state => state.name = 'newName')`\n   */\n  MutationType[\"patchFunction\"] = \"patch function\";\n  // maybe reset? for $state = {} and $reset\n})(MutationType || (MutationType = {}));\nconst IS_CLIENT = typeof window !== 'undefined';\n\n/*\n * FileSaver.js A saveAs() FileSaver implementation.\n *\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\n * Morote.\n *\n * License : MIT\n */\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nconst _global = /*#__PURE__*/(() => typeof window === 'object' && window.window === window ? window : typeof self === 'object' && self.self === self ? self : typeof global === 'object' && global.global === global ? global : typeof globalThis === 'object' ? globalThis : {\n  HTMLElement: null\n})();\nfunction bom(blob, {\n  autoBom = false\n} = {}) {\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xfeff), blob], {\n      type: blob.type\n    });\n  }\n  return blob;\n}\nfunction download(url, name, opts) {\n  const xhr = new XMLHttpRequest();\n  xhr.open('GET', url);\n  xhr.responseType = 'blob';\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts);\n  };\n  xhr.onerror = function () {\n    console.error('could not download file');\n  };\n  xhr.send();\n}\nfunction corsEnabled(url) {\n  const xhr = new XMLHttpRequest();\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false);\n  try {\n    xhr.send();\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299;\n}\n// `a.click()` doesn't work for all browsers (#465)\nfunction click(node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'));\n  } catch (e) {\n    const evt = new MouseEvent('click', {\n      bubbles: true,\n      cancelable: true,\n      view: window,\n      detail: 0,\n      screenX: 80,\n      screenY: 20,\n      clientX: 80,\n      clientY: 20,\n      ctrlKey: false,\n      altKey: false,\n      shiftKey: false,\n      metaKey: false,\n      button: 0,\n      relatedTarget: null\n    });\n    node.dispatchEvent(evt);\n  }\n}\nconst _navigator = typeof navigator === 'object' ? navigator : {\n  userAgent: ''\n};\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nconst isMacOSWebView = /*#__PURE__*/(() => /Macintosh/.test(_navigator.userAgent) && /AppleWebKit/.test(_navigator.userAgent) && !/Safari/.test(_navigator.userAgent))();\nconst saveAs = !IS_CLIENT ? () => {} // noop\n:\n// Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\ntypeof HTMLAnchorElement !== 'undefined' && 'download' in HTMLAnchorElement.prototype && !isMacOSWebView ? downloadSaveAs :\n// Use msSaveOrOpenBlob as a second approach\n'msSaveOrOpenBlob' in _navigator ? msSaveAs :\n// Fallback to using FileReader and a popup\nfileSaverSaveAs;\nfunction downloadSaveAs(blob, name = 'download', opts) {\n  const a = document.createElement('a');\n  a.download = name;\n  a.rel = 'noopener'; // tabnabbing\n  // TODO: detect chrome extensions & packaged apps\n  // a.target = '_blank'\n  if (typeof blob === 'string') {\n    // Support regular links\n    a.href = blob;\n    if (a.origin !== location.origin) {\n      if (corsEnabled(a.href)) {\n        download(blob, name, opts);\n      } else {\n        a.target = '_blank';\n        click(a);\n      }\n    } else {\n      click(a);\n    }\n  } else {\n    // Support blobs\n    a.href = URL.createObjectURL(blob);\n    setTimeout(function () {\n      URL.revokeObjectURL(a.href);\n    }, 4e4); // 40s\n    setTimeout(function () {\n      click(a);\n    }, 0);\n  }\n}\nfunction msSaveAs(blob, name = 'download', opts) {\n  if (typeof blob === 'string') {\n    if (corsEnabled(blob)) {\n      download(blob, name, opts);\n    } else {\n      const a = document.createElement('a');\n      a.href = blob;\n      a.target = '_blank';\n      setTimeout(function () {\n        click(a);\n      });\n    }\n  } else {\n    // @ts-ignore: works on windows\n    navigator.msSaveOrOpenBlob(bom(blob, opts), name);\n  }\n}\nfunction fileSaverSaveAs(blob, name, opts, popup) {\n  // Open a popup immediately do go around popup blocker\n  // Mostly only available on user interaction and the fileReader is async so...\n  popup = popup || open('', '_blank');\n  if (popup) {\n    popup.document.title = popup.document.body.innerText = 'downloading...';\n  }\n  if (typeof blob === 'string') return download(blob, name, opts);\n  const force = blob.type === 'application/octet-stream';\n  const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\n  const isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n  if ((isChromeIOS || force && isSafari || isMacOSWebView) && typeof FileReader !== 'undefined') {\n    // Safari doesn't allow downloading of blob URLs\n    const reader = new FileReader();\n    reader.onloadend = function () {\n      let url = reader.result;\n      if (typeof url !== 'string') {\n        popup = null;\n        throw new Error('Wrong reader.result type');\n      }\n      url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\n      if (popup) {\n        popup.location.href = url;\n      } else {\n        location.assign(url);\n      }\n      popup = null; // reverse-tabnabbing #460\n    };\n    reader.readAsDataURL(blob);\n  } else {\n    const url = URL.createObjectURL(blob);\n    if (popup) popup.location.assign(url);else location.href = url;\n    popup = null; // reverse-tabnabbing #460\n    setTimeout(function () {\n      URL.revokeObjectURL(url);\n    }, 4e4); // 40s\n  }\n}\n\n/**\n * Shows a toast or console.log\n *\n * @param message - message to log\n * @param type - different color of the tooltip\n */\nfunction toastMessage(message, type) {\n  const piniaMessage = '🍍 ' + message;\n  if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\n    // No longer available :(\n    __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\n  } else if (type === 'error') {\n    console.error(piniaMessage);\n  } else if (type === 'warn') {\n    console.warn(piniaMessage);\n  } else {\n    console.log(piniaMessage);\n  }\n}\nfunction isPinia(o) {\n  return '_a' in o && 'install' in o;\n}\n\n/**\n * This file contain devtools actions, they are not Pinia actions.\n */\n// ---\nfunction checkClipboardAccess() {\n  if (!('clipboard' in navigator)) {\n    toastMessage(`Your browser doesn't support the Clipboard API`, 'error');\n    return true;\n  }\n}\nfunction checkNotFocusedError(error) {\n  if (error instanceof Error && error.message.toLowerCase().includes('document is not focused')) {\n    toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\n    return true;\n  }\n  return false;\n}\nasync function actionGlobalCopyState(pinia) {\n  if (checkClipboardAccess()) return;\n  try {\n    await navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\n    toastMessage('Global state copied to clipboard.');\n  } catch (error) {\n    if (checkNotFocusedError(error)) return;\n    toastMessage(`Failed to serialize the state. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nasync function actionGlobalPasteState(pinia) {\n  if (checkClipboardAccess()) return;\n  try {\n    loadStoresState(pinia, JSON.parse(await navigator.clipboard.readText()));\n    toastMessage('Global state pasted from clipboard.');\n  } catch (error) {\n    if (checkNotFocusedError(error)) return;\n    toastMessage(`Failed to deserialize the state from clipboard. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nasync function actionGlobalSaveState(pinia) {\n  try {\n    saveAs(new Blob([JSON.stringify(pinia.state.value)], {\n      type: 'text/plain;charset=utf-8'\n    }), 'pinia-state.json');\n  } catch (error) {\n    toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nlet fileInput;\nfunction getFileOpener() {\n  if (!fileInput) {\n    fileInput = document.createElement('input');\n    fileInput.type = 'file';\n    fileInput.accept = '.json';\n  }\n  function openFile() {\n    return new Promise((resolve, reject) => {\n      fileInput.onchange = async () => {\n        const files = fileInput.files;\n        if (!files) return resolve(null);\n        const file = files.item(0);\n        if (!file) return resolve(null);\n        return resolve({\n          text: await file.text(),\n          file\n        });\n      };\n      // @ts-ignore: TODO: changed from 4.3 to 4.4\n      fileInput.oncancel = () => resolve(null);\n      fileInput.onerror = reject;\n      fileInput.click();\n    });\n  }\n  return openFile;\n}\nasync function actionGlobalOpenStateFile(pinia) {\n  try {\n    const open = getFileOpener();\n    const result = await open();\n    if (!result) return;\n    const {\n      text,\n      file\n    } = result;\n    loadStoresState(pinia, JSON.parse(text));\n    toastMessage(`Global state imported from \"${file.name}\".`);\n  } catch (error) {\n    toastMessage(`Failed to import the state from JSON. Check the console for more details.`, 'error');\n    console.error(error);\n  }\n}\nfunction loadStoresState(pinia, state) {\n  for (const key in state) {\n    const storeState = pinia.state.value[key];\n    // store is already instantiated, patch it\n    if (storeState) {\n      Object.assign(storeState, state[key]);\n    } else {\n      // store is not instantiated, set the initial state\n      pinia.state.value[key] = state[key];\n    }\n  }\n}\nfunction formatDisplay(display) {\n  return {\n    _custom: {\n      display\n    }\n  };\n}\nconst PINIA_ROOT_LABEL = '🍍 Pinia (root)';\nconst PINIA_ROOT_ID = '_root';\nfunction formatStoreForInspectorTree(store) {\n  return isPinia(store) ? {\n    id: PINIA_ROOT_ID,\n    label: PINIA_ROOT_LABEL\n  } : {\n    id: store.$id,\n    label: store.$id\n  };\n}\nfunction formatStoreForInspectorState(store) {\n  if (isPinia(store)) {\n    const storeNames = Array.from(store._s.keys());\n    const storeMap = store._s;\n    const state = {\n      state: storeNames.map(storeId => ({\n        editable: true,\n        key: storeId,\n        value: store.state.value[storeId]\n      })),\n      getters: storeNames.filter(id => storeMap.get(id)._getters).map(id => {\n        const store = storeMap.get(id);\n        return {\n          editable: false,\n          key: id,\n          value: store._getters.reduce((getters, key) => {\n            getters[key] = store[key];\n            return getters;\n          }, {})\n        };\n      })\n    };\n    return state;\n  }\n  const state = {\n    state: Object.keys(store.$state).map(key => ({\n      editable: true,\n      key,\n      value: store.$state[key]\n    }))\n  };\n  // avoid adding empty getters\n  if (store._getters && store._getters.length) {\n    state.getters = store._getters.map(getterName => ({\n      editable: false,\n      key: getterName,\n      value: store[getterName]\n    }));\n  }\n  if (store._customProperties.size) {\n    state.customProperties = Array.from(store._customProperties).map(key => ({\n      editable: true,\n      key,\n      value: store[key]\n    }));\n  }\n  return state;\n}\nfunction formatEventData(events) {\n  if (!events) return {};\n  if (Array.isArray(events)) {\n    // TODO: handle add and delete for arrays and objects\n    return events.reduce((data, event) => {\n      data.keys.push(event.key);\n      data.operations.push(event.type);\n      data.oldValue[event.key] = event.oldValue;\n      data.newValue[event.key] = event.newValue;\n      return data;\n    }, {\n      oldValue: {},\n      keys: [],\n      operations: [],\n      newValue: {}\n    });\n  } else {\n    return {\n      operation: formatDisplay(events.type),\n      key: formatDisplay(events.key),\n      oldValue: events.oldValue,\n      newValue: events.newValue\n    };\n  }\n}\nfunction formatMutationType(type) {\n  switch (type) {\n    case MutationType.direct:\n      return 'mutation';\n    case MutationType.patchFunction:\n      return '$patch';\n    case MutationType.patchObject:\n      return '$patch';\n    default:\n      return 'unknown';\n  }\n}\n\n// timeline can be paused when directly changing the state\nlet isTimelineActive = true;\nconst componentStateTypes = [];\nconst MUTATIONS_LAYER_ID = 'pinia:mutations';\nconst INSPECTOR_ID = 'pinia';\nconst {\n  assign: assign$1\n} = Object;\n/**\n * Gets the displayed name of a store in devtools\n *\n * @param id - id of the store\n * @returns a formatted string\n */\nconst getStoreType = id => '🍍 ' + id;\n/**\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\n * as soon as it is added to the application.\n *\n * @param app - Vue application\n * @param pinia - pinia instance\n */\nfunction registerPiniaDevtools(app, pinia) {\n  setupDevtoolsPlugin({\n    id: 'dev.esm.pinia',\n    label: 'Pinia 🍍',\n    logo: 'https://pinia.vuejs.org/logo.svg',\n    packageName: 'pinia',\n    homepage: 'https://pinia.vuejs.org',\n    componentStateTypes,\n    app\n  }, api => {\n    if (typeof api.now !== 'function') {\n      toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n    }\n    api.addTimelineLayer({\n      id: MUTATIONS_LAYER_ID,\n      label: `Pinia 🍍`,\n      color: 0xe5df88\n    });\n    api.addInspector({\n      id: INSPECTOR_ID,\n      label: 'Pinia 🍍',\n      icon: 'storage',\n      treeFilterPlaceholder: 'Search stores',\n      actions: [{\n        icon: 'content_copy',\n        action: () => {\n          actionGlobalCopyState(pinia);\n        },\n        tooltip: 'Serialize and copy the state'\n      }, {\n        icon: 'content_paste',\n        action: async () => {\n          await actionGlobalPasteState(pinia);\n          api.sendInspectorTree(INSPECTOR_ID);\n          api.sendInspectorState(INSPECTOR_ID);\n        },\n        tooltip: 'Replace the state with the content of your clipboard'\n      }, {\n        icon: 'save',\n        action: () => {\n          actionGlobalSaveState(pinia);\n        },\n        tooltip: 'Save the state as a JSON file'\n      }, {\n        icon: 'folder_open',\n        action: async () => {\n          await actionGlobalOpenStateFile(pinia);\n          api.sendInspectorTree(INSPECTOR_ID);\n          api.sendInspectorState(INSPECTOR_ID);\n        },\n        tooltip: 'Import the state from a JSON file'\n      }],\n      nodeActions: [{\n        icon: 'restore',\n        tooltip: 'Reset the state (with \"$reset\")',\n        action: nodeId => {\n          const store = pinia._s.get(nodeId);\n          if (!store) {\n            toastMessage(`Cannot reset \"${nodeId}\" store because it wasn't found.`, 'warn');\n          } else if (typeof store.$reset !== 'function') {\n            toastMessage(`Cannot reset \"${nodeId}\" store because it doesn't have a \"$reset\" method implemented.`, 'warn');\n          } else {\n            store.$reset();\n            toastMessage(`Store \"${nodeId}\" reset.`);\n          }\n        }\n      }]\n    });\n    api.on.inspectComponent(payload => {\n      const proxy = payload.componentInstance && payload.componentInstance.proxy;\n      if (proxy && proxy._pStores) {\n        const piniaStores = payload.componentInstance.proxy._pStores;\n        Object.values(piniaStores).forEach(store => {\n          payload.instanceData.state.push({\n            type: getStoreType(store.$id),\n            key: 'state',\n            editable: true,\n            value: store._isOptionsAPI ? {\n              _custom: {\n                value: toRaw(store.$state),\n                actions: [{\n                  icon: 'restore',\n                  tooltip: 'Reset the state of this store',\n                  action: () => store.$reset()\n                }]\n              }\n            } :\n            // NOTE: workaround to unwrap transferred refs\n            Object.keys(store.$state).reduce((state, key) => {\n              state[key] = store.$state[key];\n              return state;\n            }, {})\n          });\n          if (store._getters && store._getters.length) {\n            payload.instanceData.state.push({\n              type: getStoreType(store.$id),\n              key: 'getters',\n              editable: false,\n              value: store._getters.reduce((getters, key) => {\n                try {\n                  getters[key] = store[key];\n                } catch (error) {\n                  // @ts-expect-error: we just want to show it in devtools\n                  getters[key] = error;\n                }\n                return getters;\n              }, {})\n            });\n          }\n        });\n      }\n    });\n    api.on.getInspectorTree(payload => {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        let stores = [pinia];\n        stores = stores.concat(Array.from(pinia._s.values()));\n        payload.rootNodes = (payload.filter ? stores.filter(store => '$id' in store ? store.$id.toLowerCase().includes(payload.filter.toLowerCase()) : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase())) : stores).map(formatStoreForInspectorTree);\n      }\n    });\n    // Expose pinia instance as $pinia to window\n    globalThis.$pinia = pinia;\n    api.on.getInspectorState(payload => {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        const inspectedStore = payload.nodeId === PINIA_ROOT_ID ? pinia : pinia._s.get(payload.nodeId);\n        if (!inspectedStore) {\n          // this could be the selected store restored for a different project\n          // so it's better not to say anything here\n          return;\n        }\n        if (inspectedStore) {\n          // Expose selected store as $store to window\n          if (payload.nodeId !== PINIA_ROOT_ID) globalThis.$store = toRaw(inspectedStore);\n          payload.state = formatStoreForInspectorState(inspectedStore);\n        }\n      }\n    });\n    api.on.editInspectorState(payload => {\n      if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n        const inspectedStore = payload.nodeId === PINIA_ROOT_ID ? pinia : pinia._s.get(payload.nodeId);\n        if (!inspectedStore) {\n          return toastMessage(`store \"${payload.nodeId}\" not found`, 'error');\n        }\n        const {\n          path\n        } = payload;\n        if (!isPinia(inspectedStore)) {\n          // access only the state\n          if (path.length !== 1 || !inspectedStore._customProperties.has(path[0]) || path[0] in inspectedStore.$state) {\n            path.unshift('$state');\n          }\n        } else {\n          // Root access, we can omit the `.value` because the devtools API does it for us\n          path.unshift('state');\n        }\n        isTimelineActive = false;\n        payload.set(inspectedStore, path, payload.state.value);\n        isTimelineActive = true;\n      }\n    });\n    api.on.editComponentState(payload => {\n      if (payload.type.startsWith('🍍')) {\n        const storeId = payload.type.replace(/^🍍\\s*/, '');\n        const store = pinia._s.get(storeId);\n        if (!store) {\n          return toastMessage(`store \"${storeId}\" not found`, 'error');\n        }\n        const {\n          path\n        } = payload;\n        if (path[0] !== 'state') {\n          return toastMessage(`Invalid path for store \"${storeId}\":\\n${path}\\nOnly state can be modified.`);\n        }\n        // rewrite the first entry to be able to directly set the state as\n        // well as any other path\n        path[0] = '$state';\n        isTimelineActive = false;\n        payload.set(store, path, payload.state.value);\n        isTimelineActive = true;\n      }\n    });\n  });\n}\nfunction addStoreToDevtools(app, store) {\n  if (!componentStateTypes.includes(getStoreType(store.$id))) {\n    componentStateTypes.push(getStoreType(store.$id));\n  }\n  setupDevtoolsPlugin({\n    id: 'dev.esm.pinia',\n    label: 'Pinia 🍍',\n    logo: 'https://pinia.vuejs.org/logo.svg',\n    packageName: 'pinia',\n    homepage: 'https://pinia.vuejs.org',\n    componentStateTypes,\n    app,\n    settings: {\n      logStoreChanges: {\n        label: 'Notify about new/deleted stores',\n        type: 'boolean',\n        defaultValue: true\n      }\n      // useEmojis: {\n      //   label: 'Use emojis in messages ⚡️',\n      //   type: 'boolean',\n      //   defaultValue: true,\n      // },\n    }\n  }, api => {\n    // gracefully handle errors\n    const now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\n    store.$onAction(({\n      after,\n      onError,\n      name,\n      args\n    }) => {\n      const groupId = runningActionId++;\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: {\n          time: now(),\n          title: '🛫 ' + name,\n          subtitle: 'start',\n          data: {\n            store: formatDisplay(store.$id),\n            action: formatDisplay(name),\n            args\n          },\n          groupId\n        }\n      });\n      after(result => {\n        activeAction = undefined;\n        api.addTimelineEvent({\n          layerId: MUTATIONS_LAYER_ID,\n          event: {\n            time: now(),\n            title: '🛬 ' + name,\n            subtitle: 'end',\n            data: {\n              store: formatDisplay(store.$id),\n              action: formatDisplay(name),\n              args,\n              result\n            },\n            groupId\n          }\n        });\n      });\n      onError(error => {\n        activeAction = undefined;\n        api.addTimelineEvent({\n          layerId: MUTATIONS_LAYER_ID,\n          event: {\n            time: now(),\n            logType: 'error',\n            title: '💥 ' + name,\n            subtitle: 'end',\n            data: {\n              store: formatDisplay(store.$id),\n              action: formatDisplay(name),\n              args,\n              error\n            },\n            groupId\n          }\n        });\n      });\n    }, true);\n    store._customProperties.forEach(name => {\n      watch(() => unref(store[name]), (newValue, oldValue) => {\n        api.notifyComponentUpdate();\n        api.sendInspectorState(INSPECTOR_ID);\n        if (isTimelineActive) {\n          api.addTimelineEvent({\n            layerId: MUTATIONS_LAYER_ID,\n            event: {\n              time: now(),\n              title: 'Change',\n              subtitle: name,\n              data: {\n                newValue,\n                oldValue\n              },\n              groupId: activeAction\n            }\n          });\n        }\n      }, {\n        deep: true\n      });\n    });\n    store.$subscribe(({\n      events,\n      type\n    }, state) => {\n      api.notifyComponentUpdate();\n      api.sendInspectorState(INSPECTOR_ID);\n      if (!isTimelineActive) return;\n      // rootStore.state[store.id] = state\n      const eventData = {\n        time: now(),\n        title: formatMutationType(type),\n        data: assign$1({\n          store: formatDisplay(store.$id)\n        }, formatEventData(events)),\n        groupId: activeAction\n      };\n      if (type === MutationType.patchFunction) {\n        eventData.subtitle = '⤵️';\n      } else if (type === MutationType.patchObject) {\n        eventData.subtitle = '🧩';\n      } else if (events && !Array.isArray(events)) {\n        eventData.subtitle = events.type;\n      }\n      if (events) {\n        eventData.data['rawEvent(s)'] = {\n          _custom: {\n            display: 'DebuggerEvent',\n            type: 'object',\n            tooltip: 'raw DebuggerEvent[]',\n            value: events\n          }\n        };\n      }\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: eventData\n      });\n    }, {\n      detached: true,\n      flush: 'sync'\n    });\n    const hotUpdate = store._hotUpdate;\n    store._hotUpdate = markRaw(newStore => {\n      hotUpdate(newStore);\n      api.addTimelineEvent({\n        layerId: MUTATIONS_LAYER_ID,\n        event: {\n          time: now(),\n          title: '🔥 ' + store.$id,\n          subtitle: 'HMR update',\n          data: {\n            store: formatDisplay(store.$id),\n            info: formatDisplay(`HMR update`)\n          }\n        }\n      });\n      // update the devtools too\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(INSPECTOR_ID);\n      api.sendInspectorState(INSPECTOR_ID);\n    });\n    const {\n      $dispose\n    } = store;\n    store.$dispose = () => {\n      $dispose();\n      api.notifyComponentUpdate();\n      api.sendInspectorTree(INSPECTOR_ID);\n      api.sendInspectorState(INSPECTOR_ID);\n      api.getSettings().logStoreChanges && toastMessage(`Disposed \"${store.$id}\" store 🗑`);\n    };\n    // trigger an update so it can display new registered stores\n    api.notifyComponentUpdate();\n    api.sendInspectorTree(INSPECTOR_ID);\n    api.sendInspectorState(INSPECTOR_ID);\n    api.getSettings().logStoreChanges && toastMessage(`\"${store.$id}\" store installed 🆕`);\n  });\n}\nlet runningActionId = 0;\nlet activeAction;\n/**\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\n * mutation to the action.\n *\n * @param store - store to patch\n * @param actionNames - list of actionst to patch\n */\nfunction patchActionForGrouping(store, actionNames, wrapWithProxy) {\n  // original actions of the store as they are given by pinia. We are going to override them\n  const actions = actionNames.reduce((storeActions, actionName) => {\n    // use toRaw to avoid tracking #541\n    storeActions[actionName] = toRaw(store)[actionName];\n    return storeActions;\n  }, {});\n  for (const actionName in actions) {\n    store[actionName] = function () {\n      // the running action id is incremented in a before action hook\n      const _actionId = runningActionId;\n      const trackedStore = wrapWithProxy ? new Proxy(store, {\n        get(...args) {\n          activeAction = _actionId;\n          return Reflect.get(...args);\n        },\n        set(...args) {\n          activeAction = _actionId;\n          return Reflect.set(...args);\n        }\n      }) : store;\n      // For Setup Stores we need https://github.com/tc39/proposal-async-context\n      activeAction = _actionId;\n      const retValue = actions[actionName].apply(trackedStore, arguments);\n      // this is safer as async actions in Setup Stores would associate mutations done outside of the action\n      activeAction = undefined;\n      return retValue;\n    };\n  }\n}\n/**\n * pinia.use(devtoolsPlugin)\n */\nfunction devtoolsPlugin({\n  app,\n  store,\n  options\n}) {\n  // HMR module\n  if (store.$id.startsWith('__hot:')) {\n    return;\n  }\n  // detect option api vs setup api\n  store._isOptionsAPI = !!options.state;\n  // Do not overwrite actions mocked by @pinia/testing (#2298)\n  if (!store._p._testing) {\n    patchActionForGrouping(store, Object.keys(options.actions), store._isOptionsAPI);\n    // Upgrade the HMR to also update the new actions\n    const originalHotUpdate = store._hotUpdate;\n    toRaw(store)._hotUpdate = function (newStore) {\n      originalHotUpdate.apply(this, arguments);\n      patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions), !!store._isOptionsAPI);\n    };\n  }\n  addStoreToDevtools(app,\n  // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\n  store);\n}\n\n/**\n * Creates a Pinia instance to be used by the application\n */\nfunction createPinia() {\n  const scope = effectScope(true);\n  // NOTE: here we could check the window object for a state and directly set it\n  // if there is anything like it with Vue 3 SSR\n  const state = scope.run(() => ref({}));\n  let _p = [];\n  // plugins added before calling app.use(pinia)\n  let toBeInstalled = [];\n  const pinia = markRaw({\n    install(app) {\n      // this allows calling useStore() outside of a component setup after\n      // installing pinia's plugin\n      setActivePinia(pinia);\n      pinia._a = app;\n      app.provide(piniaSymbol, pinia);\n      app.config.globalProperties.$pinia = pinia;\n      /* istanbul ignore else */\n      if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n        registerPiniaDevtools(app, pinia);\n      }\n      toBeInstalled.forEach(plugin => _p.push(plugin));\n      toBeInstalled = [];\n    },\n    use(plugin) {\n      if (!this._a) {\n        toBeInstalled.push(plugin);\n      } else {\n        _p.push(plugin);\n      }\n      return this;\n    },\n    _p,\n    // it's actually undefined here\n    // @ts-expect-error\n    _a: null,\n    _e: scope,\n    _s: new Map(),\n    state\n  });\n  // pinia devtools rely on dev only features so they cannot be forced unless\n  // the dev build of Vue is used. Avoid old browsers like IE11.\n  if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT && typeof Proxy !== 'undefined') {\n    pinia.use(devtoolsPlugin);\n  }\n  return pinia;\n}\n/**\n * Dispose a Pinia instance by stopping its effectScope and removing the state, plugins and stores. This is mostly\n * useful in tests, with both a testing pinia or a regular pinia and in applications that use multiple pinia instances.\n * Once disposed, the pinia instance cannot be used anymore.\n *\n * @param pinia - pinia instance\n */\nfunction disposePinia(pinia) {\n  pinia._e.stop();\n  pinia._s.clear();\n  pinia._p.splice(0);\n  pinia.state.value = {};\n  // @ts-expect-error: non valid\n  pinia._a = null;\n}\n\n/**\n * Checks if a function is a `StoreDefinition`.\n *\n * @param fn - object to test\n * @returns true if `fn` is a StoreDefinition\n */\nconst isUseStore = fn => {\n  return typeof fn === 'function' && typeof fn.$id === 'string';\n};\n/**\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\n * remove any key not existing in `newState` and recursively merge plain\n * objects.\n *\n * @param newState - new state object to be patched\n * @param oldState - old state that should be used to patch newState\n * @returns - newState\n */\nfunction patchObject(newState, oldState) {\n  // no need to go through symbols because they cannot be serialized anyway\n  for (const key in oldState) {\n    const subPatch = oldState[key];\n    // skip the whole sub tree\n    if (!(key in newState)) {\n      continue;\n    }\n    const targetValue = newState[key];\n    if (isPlainObject(targetValue) && isPlainObject(subPatch) && !isRef(subPatch) && !isReactive(subPatch)) {\n      newState[key] = patchObject(targetValue, subPatch);\n    } else {\n      // objects are either a bit more complex (e.g. refs) or primitives, so we\n      // just set the whole thing\n      newState[key] = subPatch;\n    }\n  }\n  return newState;\n}\n/**\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\n *\n * @example\n * ```js\n * const useUser = defineStore(...)\n * if (import.meta.hot) {\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\n * }\n * ```\n *\n * @param initialUseStore - return of the defineStore to hot update\n * @param hot - `import.meta.hot`\n */\nfunction acceptHMRUpdate(initialUseStore, hot) {\n  // strip as much as possible from iife.prod\n  if (!(process.env.NODE_ENV !== 'production')) {\n    return () => {};\n  }\n  return newModule => {\n    const pinia = hot.data.pinia || initialUseStore._pinia;\n    if (!pinia) {\n      // this store is still not used\n      return;\n    }\n    // preserve the pinia instance across loads\n    hot.data.pinia = pinia;\n    // console.log('got data', newStore)\n    for (const exportName in newModule) {\n      const useStore = newModule[exportName];\n      // console.log('checking for', exportName)\n      if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\n        // console.log('Accepting update for', useStore.$id)\n        const id = useStore.$id;\n        if (id !== initialUseStore.$id) {\n          console.warn(`The id of the store changed from \"${initialUseStore.$id}\" to \"${id}\". Reloading.`);\n          // return import.meta.hot.invalidate()\n          return hot.invalidate();\n        }\n        const existingStore = pinia._s.get(id);\n        if (!existingStore) {\n          console.log(`[Pinia]: skipping hmr because store doesn't exist yet`);\n          return;\n        }\n        useStore(pinia, existingStore);\n      }\n    }\n  };\n}\nconst noop = () => {};\nfunction addSubscription(subscriptions, callback, detached, onCleanup = noop) {\n  subscriptions.push(callback);\n  const removeSubscription = () => {\n    const idx = subscriptions.indexOf(callback);\n    if (idx > -1) {\n      subscriptions.splice(idx, 1);\n      onCleanup();\n    }\n  };\n  if (!detached && getCurrentScope()) {\n    onScopeDispose(removeSubscription);\n  }\n  return removeSubscription;\n}\nfunction triggerSubscriptions(subscriptions, ...args) {\n  subscriptions.slice().forEach(callback => {\n    callback(...args);\n  });\n}\nconst fallbackRunWithContext = fn => fn();\n/**\n * Marks a function as an action for `$onAction`\n * @internal\n */\nconst ACTION_MARKER = Symbol();\n/**\n * Action name symbol. Allows to add a name to an action after defining it\n * @internal\n */\nconst ACTION_NAME = Symbol();\nfunction mergeReactiveObjects(target, patchToApply) {\n  // Handle Map instances\n  if (target instanceof Map && patchToApply instanceof Map) {\n    patchToApply.forEach((value, key) => target.set(key, value));\n  } else if (target instanceof Set && patchToApply instanceof Set) {\n    // Handle Set instances\n    patchToApply.forEach(target.add, target);\n  }\n  // no need to go through symbols because they cannot be serialized anyway\n  for (const key in patchToApply) {\n    if (!patchToApply.hasOwnProperty(key)) continue;\n    const subPatch = patchToApply[key];\n    const targetValue = target[key];\n    if (isPlainObject(targetValue) && isPlainObject(subPatch) && target.hasOwnProperty(key) && !isRef(subPatch) && !isReactive(subPatch)) {\n      // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\n      // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\n      // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\n      target[key] = mergeReactiveObjects(targetValue, subPatch);\n    } else {\n      // @ts-expect-error: subPatch is a valid value\n      target[key] = subPatch;\n    }\n  }\n  return target;\n}\nconst skipHydrateSymbol = process.env.NODE_ENV !== 'production' ? Symbol('pinia:skipHydration') : /* istanbul ignore next */Symbol();\n/**\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\n *\n * @param obj - target object\n * @returns obj\n */\nfunction skipHydrate(obj) {\n  return Object.defineProperty(obj, skipHydrateSymbol, {});\n}\n/**\n * Returns whether a value should be hydrated\n *\n * @param obj - target variable\n * @returns true if `obj` should be hydrated\n */\nfunction shouldHydrate(obj) {\n  return !isPlainObject(obj) || !Object.prototype.hasOwnProperty.call(obj, skipHydrateSymbol);\n}\nconst {\n  assign\n} = Object;\nfunction isComputed(o) {\n  return !!(isRef(o) && o.effect);\n}\nfunction createOptionsStore(id, options, pinia, hot) {\n  const {\n    state,\n    actions,\n    getters\n  } = options;\n  const initialState = pinia.state.value[id];\n  let store;\n  function setup() {\n    if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n      /* istanbul ignore if */\n      pinia.state.value[id] = state ? state() : {};\n    }\n    // avoid creating a state in pinia.state.value\n    const localState = process.env.NODE_ENV !== 'production' && hot ?\n    // use ref() to unwrap refs inside state TODO: check if this is still necessary\n    toRefs(ref(state ? state() : {}).value) : toRefs(pinia.state.value[id]);\n    return assign(localState, actions, Object.keys(getters || {}).reduce((computedGetters, name) => {\n      if (process.env.NODE_ENV !== 'production' && name in localState) {\n        console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with \"${name}\" in store \"${id}\".`);\n      }\n      computedGetters[name] = markRaw(computed(() => {\n        setActivePinia(pinia);\n        // it was created just before\n        const store = pinia._s.get(id);\n        // allow cross using stores\n        // @ts-expect-error\n        // return getters![name].call(context, context)\n        // TODO: avoid reading the getter while assigning with a global variable\n        return getters[name].call(store, store);\n      }));\n      return computedGetters;\n    }, {}));\n  }\n  store = createSetupStore(id, setup, options, pinia, hot, true);\n  return store;\n}\nfunction createSetupStore($id, setup, options = {}, pinia, hot, isOptionsStore) {\n  let scope;\n  const optionsForPlugin = assign({\n    actions: {}\n  }, options);\n  /* istanbul ignore if */\n  if (process.env.NODE_ENV !== 'production' && !pinia._e.active) {\n    throw new Error('Pinia destroyed');\n  }\n  // watcher options for $subscribe\n  const $subscribeOptions = {\n    deep: true\n  };\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    $subscribeOptions.onTrigger = event => {\n      /* istanbul ignore else */\n      if (isListening) {\n        debuggerEvents = event;\n        // avoid triggering this while the store is being built and the state is being set in pinia\n      } else if (isListening == false && !store._hotUpdating) {\n        // let patch send all the events together later\n        /* istanbul ignore else */\n        if (Array.isArray(debuggerEvents)) {\n          debuggerEvents.push(event);\n        } else {\n          console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\n        }\n      }\n    };\n  }\n  // internal state\n  let isListening; // set to true at the end\n  let isSyncListening; // set to true at the end\n  let subscriptions = [];\n  let actionSubscriptions = [];\n  let debuggerEvents;\n  const initialState = pinia.state.value[$id];\n  // avoid setting the state for option stores if it is set\n  // by the setup\n  if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n    /* istanbul ignore if */\n    pinia.state.value[$id] = {};\n  }\n  const hotState = ref({});\n  // avoid triggering too many listeners\n  // https://github.com/vuejs/pinia/issues/1129\n  let activeListener;\n  function $patch(partialStateOrMutator) {\n    let subscriptionMutation;\n    isListening = isSyncListening = false;\n    // reset the debugger events since patches are sync\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      debuggerEvents = [];\n    }\n    if (typeof partialStateOrMutator === 'function') {\n      partialStateOrMutator(pinia.state.value[$id]);\n      subscriptionMutation = {\n        type: MutationType.patchFunction,\n        storeId: $id,\n        events: debuggerEvents\n      };\n    } else {\n      mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\n      subscriptionMutation = {\n        type: MutationType.patchObject,\n        payload: partialStateOrMutator,\n        storeId: $id,\n        events: debuggerEvents\n      };\n    }\n    const myListenerId = activeListener = Symbol();\n    nextTick().then(() => {\n      if (activeListener === myListenerId) {\n        isListening = true;\n      }\n    });\n    isSyncListening = true;\n    // because we paused the watcher, we need to manually call the subscriptions\n    triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\n  }\n  const $reset = isOptionsStore ? function $reset() {\n    const {\n      state\n    } = options;\n    const newState = state ? state() : {};\n    // we use a patch to group all changes into one single subscription\n    this.$patch($state => {\n      // @ts-expect-error: FIXME: shouldn't error?\n      assign($state, newState);\n    });\n  } : /* istanbul ignore next */\n  process.env.NODE_ENV !== 'production' ? () => {\n    throw new Error(`🍍: Store \"${$id}\" is built using the setup syntax and does not implement $reset().`);\n  } : noop;\n  function $dispose() {\n    scope.stop();\n    subscriptions = [];\n    actionSubscriptions = [];\n    pinia._s.delete($id);\n  }\n  /**\n   * Helper that wraps function so it can be tracked with $onAction\n   * @param fn - action to wrap\n   * @param name - name of the action\n   */\n  const action = (fn, name = '') => {\n    if (ACTION_MARKER in fn) {\n      fn[ACTION_NAME] = name;\n      return fn;\n    }\n    const wrappedAction = function () {\n      setActivePinia(pinia);\n      const args = Array.from(arguments);\n      const afterCallbackList = [];\n      const onErrorCallbackList = [];\n      function after(callback) {\n        afterCallbackList.push(callback);\n      }\n      function onError(callback) {\n        onErrorCallbackList.push(callback);\n      }\n      // @ts-expect-error\n      triggerSubscriptions(actionSubscriptions, {\n        args,\n        name: wrappedAction[ACTION_NAME],\n        store,\n        after,\n        onError\n      });\n      let ret;\n      try {\n        ret = fn.apply(this && this.$id === $id ? this : store, args);\n        // handle sync errors\n      } catch (error) {\n        triggerSubscriptions(onErrorCallbackList, error);\n        throw error;\n      }\n      if (ret instanceof Promise) {\n        return ret.then(value => {\n          triggerSubscriptions(afterCallbackList, value);\n          return value;\n        }).catch(error => {\n          triggerSubscriptions(onErrorCallbackList, error);\n          return Promise.reject(error);\n        });\n      }\n      // trigger after callbacks\n      triggerSubscriptions(afterCallbackList, ret);\n      return ret;\n    };\n    wrappedAction[ACTION_MARKER] = true;\n    wrappedAction[ACTION_NAME] = name; // will be set later\n    // @ts-expect-error: we are intentionally limiting the returned type to just Fn\n    // because all the added properties are internals that are exposed through `$onAction()` only\n    return wrappedAction;\n  };\n  const _hmrPayload = /*#__PURE__*/markRaw({\n    actions: {},\n    getters: {},\n    state: [],\n    hotState\n  });\n  const partialStore = {\n    _p: pinia,\n    // _s: scope,\n    $id,\n    $onAction: addSubscription.bind(null, actionSubscriptions),\n    $patch,\n    $reset,\n    $subscribe(callback, options = {}) {\n      const removeSubscription = addSubscription(subscriptions, callback, options.detached, () => stopWatcher());\n      const stopWatcher = scope.run(() => watch(() => pinia.state.value[$id], state => {\n        if (options.flush === 'sync' ? isSyncListening : isListening) {\n          callback({\n            storeId: $id,\n            type: MutationType.direct,\n            events: debuggerEvents\n          }, state);\n        }\n      }, assign({}, $subscribeOptions, options)));\n      return removeSubscription;\n    },\n    $dispose\n  };\n  const store = reactive(process.env.NODE_ENV !== 'production' || (process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT ? assign({\n    _hmrPayload,\n    _customProperties: markRaw(new Set()) // devtools custom properties\n  }, partialStore\n  // must be added later\n  // setupStore\n  ) : partialStore);\n  // store the partial store now so the setup of stores can instantiate each other before they are finished without\n  // creating infinite loops.\n  pinia._s.set($id, store);\n  const runWithContext = pinia._a && pinia._a.runWithContext || fallbackRunWithContext;\n  // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\n  const setupStore = runWithContext(() => pinia._e.run(() => (scope = effectScope()).run(() => setup({\n    action\n  }))));\n  // overwrite existing actions to support $onAction\n  for (const key in setupStore) {\n    const prop = setupStore[key];\n    if (isRef(prop) && !isComputed(prop) || isReactive(prop)) {\n      // mark it as a piece of state to be serialized\n      if (process.env.NODE_ENV !== 'production' && hot) {\n        hotState.value[key] = toRef(setupStore, key);\n        // createOptionStore directly sets the state in pinia.state.value so we\n        // can just skip that\n      } else if (!isOptionsStore) {\n        // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\n        if (initialState && shouldHydrate(prop)) {\n          if (isRef(prop)) {\n            prop.value = initialState[key];\n          } else {\n            // probably a reactive object, lets recursively assign\n            // @ts-expect-error: prop is unknown\n            mergeReactiveObjects(prop, initialState[key]);\n          }\n        }\n        // transfer the ref to the pinia state to keep everything in sync\n        pinia.state.value[$id][key] = prop;\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        _hmrPayload.state.push(key);\n      }\n      // action\n    } else if (typeof prop === 'function') {\n      const actionValue = process.env.NODE_ENV !== 'production' && hot ? prop : action(prop, key);\n      // this a hot module replacement store because the hotUpdate method needs\n      // to do it with the right context\n      // @ts-expect-error\n      setupStore[key] = actionValue;\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        _hmrPayload.actions[key] = prop;\n      }\n      // list actions so they can be used in plugins\n      // @ts-expect-error\n      optionsForPlugin.actions[key] = prop;\n    } else if (process.env.NODE_ENV !== 'production') {\n      // add getters for devtools\n      if (isComputed(prop)) {\n        _hmrPayload.getters[key] = isOptionsStore ?\n        // @ts-expect-error\n        options.getters[key] : prop;\n        if (IS_CLIENT) {\n          const getters = setupStore._getters || (\n          // @ts-expect-error: same\n          setupStore._getters = markRaw([]));\n          getters.push(key);\n        }\n      }\n    }\n  }\n  // add the state, getters, and action properties\n  /* istanbul ignore if */\n  assign(store, setupStore);\n  // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\n  // Make `storeToRefs()` work with `reactive()` #799\n  assign(toRaw(store), setupStore);\n  // use this instead of a computed with setter to be able to create it anywhere\n  // without linking the computed lifespan to wherever the store is first\n  // created.\n  Object.defineProperty(store, '$state', {\n    get: () => process.env.NODE_ENV !== 'production' && hot ? hotState.value : pinia.state.value[$id],\n    set: state => {\n      /* istanbul ignore if */\n      if (process.env.NODE_ENV !== 'production' && hot) {\n        throw new Error('cannot set hotState');\n      }\n      $patch($state => {\n        // @ts-expect-error: FIXME: shouldn't error?\n        assign($state, state);\n      });\n    }\n  });\n  // add the hotUpdate before plugins to allow them to override it\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    store._hotUpdate = markRaw(newStore => {\n      store._hotUpdating = true;\n      newStore._hmrPayload.state.forEach(stateKey => {\n        if (stateKey in store.$state) {\n          const newStateTarget = newStore.$state[stateKey];\n          const oldStateSource = store.$state[stateKey];\n          if (typeof newStateTarget === 'object' && isPlainObject(newStateTarget) && isPlainObject(oldStateSource)) {\n            patchObject(newStateTarget, oldStateSource);\n          } else {\n            // transfer the ref\n            newStore.$state[stateKey] = oldStateSource;\n          }\n        }\n        // patch direct access properties to allow store.stateProperty to work as\n        // store.$state.stateProperty\n        // @ts-expect-error: any type\n        store[stateKey] = toRef(newStore.$state, stateKey);\n      });\n      // remove deleted state properties\n      Object.keys(store.$state).forEach(stateKey => {\n        if (!(stateKey in newStore.$state)) {\n          // @ts-expect-error: noop if doesn't exist\n          delete store[stateKey];\n        }\n      });\n      // avoid devtools logging this as a mutation\n      isListening = false;\n      isSyncListening = false;\n      pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\n      isSyncListening = true;\n      nextTick().then(() => {\n        isListening = true;\n      });\n      for (const actionName in newStore._hmrPayload.actions) {\n        const actionFn = newStore[actionName];\n        // @ts-expect-error: actionName is a string\n        store[actionName] =\n        //\n        action(actionFn, actionName);\n      }\n      // TODO: does this work in both setup and option store?\n      for (const getterName in newStore._hmrPayload.getters) {\n        const getter = newStore._hmrPayload.getters[getterName];\n        const getterValue = isOptionsStore ?\n        // special handling of options api\n        computed(() => {\n          setActivePinia(pinia);\n          return getter.call(store, store);\n        }) : getter;\n        // @ts-expect-error: getterName is a string\n        store[getterName] =\n        //\n        getterValue;\n      }\n      // remove deleted getters\n      Object.keys(store._hmrPayload.getters).forEach(key => {\n        if (!(key in newStore._hmrPayload.getters)) {\n          // @ts-expect-error: noop if doesn't exist\n          delete store[key];\n        }\n      });\n      // remove old actions\n      Object.keys(store._hmrPayload.actions).forEach(key => {\n        if (!(key in newStore._hmrPayload.actions)) {\n          // @ts-expect-error: noop if doesn't exist\n          delete store[key];\n        }\n      });\n      // update the values used in devtools and to allow deleting new properties later on\n      store._hmrPayload = newStore._hmrPayload;\n      store._getters = newStore._getters;\n      store._hotUpdating = false;\n    });\n  }\n  if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n    const nonEnumerable = {\n      writable: true,\n      configurable: true,\n      // avoid warning on devtools trying to display this property\n      enumerable: false\n    };\n    ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach(p => {\n      Object.defineProperty(store, p, assign({\n        value: store[p]\n      }, nonEnumerable));\n    });\n  }\n  // apply all plugins\n  pinia._p.forEach(extender => {\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production' || typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__) && !(process.env.NODE_ENV === 'test') && IS_CLIENT) {\n      const extensions = scope.run(() => extender({\n        store: store,\n        app: pinia._a,\n        pinia,\n        options: optionsForPlugin\n      }));\n      Object.keys(extensions || {}).forEach(key => store._customProperties.add(key));\n      assign(store, extensions);\n    } else {\n      assign(store, scope.run(() => extender({\n        store: store,\n        app: pinia._a,\n        pinia,\n        options: optionsForPlugin\n      })));\n    }\n  });\n  if (process.env.NODE_ENV !== 'production' && store.$state && typeof store.$state === 'object' && typeof store.$state.constructor === 'function' && !store.$state.constructor.toString().includes('[native code]')) {\n    console.warn(`[🍍]: The \"state\" must be a plain object. It cannot be\\n` + `\\tstate: () => new MyClass()\\n` + `Found in store \"${store.$id}\".`);\n  }\n  // only apply hydrate to option stores with an initial state in pinia\n  if (initialState && isOptionsStore && options.hydrate) {\n    options.hydrate(store.$state, initialState);\n  }\n  isListening = true;\n  isSyncListening = true;\n  return store;\n}\n// allows unused stores to be tree shaken\n/*! #__NO_SIDE_EFFECTS__ */\nfunction defineStore(\n// TODO: add proper types from above\nid, setup, setupOptions) {\n  let options;\n  const isSetupStore = typeof setup === 'function';\n  // the option store setup will contain the actual options in this case\n  options = isSetupStore ? setupOptions : setup;\n  function useStore(pinia, hot) {\n    const hasContext = hasInjectionContext();\n    pinia =\n    // in test mode, ignore the argument provided as we can always retrieve a\n    // pinia instance with getActivePinia()\n    (process.env.NODE_ENV === 'test' && activePinia && activePinia._testing ? null : pinia) || (hasContext ? inject(piniaSymbol, null) : null);\n    if (pinia) setActivePinia(pinia);\n    if (process.env.NODE_ENV !== 'production' && !activePinia) {\n      throw new Error(`[🍍]: \"getActivePinia()\" was called but there was no active Pinia. Are you trying to use a store before calling \"app.use(pinia)\"?\\n` + `See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\\n` + `This will fail in production.`);\n    }\n    pinia = activePinia;\n    if (!pinia._s.has(id)) {\n      // creating the store registers it in `pinia._s`\n      if (isSetupStore) {\n        createSetupStore(id, setup, options, pinia);\n      } else {\n        createOptionsStore(id, options, pinia);\n      }\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        // @ts-expect-error: not the right inferred type\n        useStore._pinia = pinia;\n      }\n    }\n    const store = pinia._s.get(id);\n    if (process.env.NODE_ENV !== 'production' && hot) {\n      const hotId = '__hot:' + id;\n      const newStore = isSetupStore ? createSetupStore(hotId, setup, options, pinia, true) : createOptionsStore(hotId, assign({}, options), pinia, true);\n      hot._hotUpdate(newStore);\n      // cleanup the state properties and the store from the cache\n      delete pinia.state.value[hotId];\n      pinia._s.delete(hotId);\n    }\n    if (process.env.NODE_ENV !== 'production' && IS_CLIENT) {\n      const currentInstance = getCurrentInstance();\n      // save stores in instances to access them devtools\n      if (currentInstance && currentInstance.proxy &&\n      // avoid adding stores that are just built for hot module replacement\n      !hot) {\n        const vm = currentInstance.proxy;\n        const cache = '_pStores' in vm ? vm._pStores : vm._pStores = {};\n        cache[id] = store;\n      }\n    }\n    // StoreGeneric cannot be casted towards Store\n    return store;\n  }\n  useStore.$id = id;\n  return useStore;\n}\nlet mapStoreSuffix = 'Store';\n/**\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\n * interface if you are using TypeScript.\n *\n * @param suffix - new suffix\n */\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\n) {\n  mapStoreSuffix = suffix;\n}\n/**\n * Allows using stores without the composition API (`setup()`) by generating an\n * object to be spread in the `computed` field of a component. It accepts a list\n * of store definitions.\n *\n * @example\n * ```js\n * export default {\n *   computed: {\n *     // other computed properties\n *     ...mapStores(useUserStore, useCartStore)\n *   },\n *\n *   created() {\n *     this.userStore // store with id \"user\"\n *     this.cartStore // store with id \"cart\"\n *   }\n * }\n * ```\n *\n * @param stores - list of stores to map to an object\n */\nfunction mapStores(...stores) {\n  if (process.env.NODE_ENV !== 'production' && Array.isArray(stores[0])) {\n    console.warn(`[🍍]: Directly pass all stores to \"mapStores()\" without putting them in an array:\\n` + `Replace\\n` + `\\tmapStores([useAuthStore, useCartStore])\\n` + `with\\n` + `\\tmapStores(useAuthStore, useCartStore)\\n` + `This will fail in production if not fixed.`);\n    stores = stores[0];\n  }\n  return stores.reduce((reduced, useStore) => {\n    // @ts-expect-error: $id is added by defineStore\n    reduced[useStore.$id + mapStoreSuffix] = function () {\n      return useStore(this.$pinia);\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapState(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce((reduced, key) => {\n    reduced[key] = function () {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[key];\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce((reduced, key) => {\n    // @ts-expect-error\n    reduced[key] = function () {\n      const store = useStore(this.$pinia);\n      const storeKey = keysOrMapper[key];\n      // for some reason TS is unable to infer the type of storeKey to be a\n      // function\n      return typeof storeKey === 'function' ? storeKey.call(this, store) :\n      // @ts-expect-error: FIXME: should work?\n      store[storeKey];\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Alias for `mapState()`. You should use `mapState()` instead.\n * @deprecated use `mapState()` instead.\n */\nconst mapGetters = mapState;\n/**\n * Allows directly using actions from your store without using the composition\n * API (`setup()`) by generating an object to be spread in the `methods` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapActions(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce((reduced, key) => {\n    // @ts-expect-error\n    reduced[key] = function (...args) {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[key](...args);\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce((reduced, key) => {\n    // @ts-expect-error\n    reduced[key] = function (...args) {\n      // @ts-expect-error: FIXME: should work?\n      return useStore(this.$pinia)[keysOrMapper[key]](...args);\n    };\n    return reduced;\n  }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapWritableState(useStore, keysOrMapper) {\n  return Array.isArray(keysOrMapper) ? keysOrMapper.reduce((reduced, key) => {\n    reduced[key] = {\n      get() {\n        return useStore(this.$pinia)[key];\n      },\n      set(value) {\n        return useStore(this.$pinia)[key] = value;\n      }\n    };\n    return reduced;\n  }, {}) : Object.keys(keysOrMapper).reduce((reduced, key) => {\n    reduced[key] = {\n      get() {\n        return useStore(this.$pinia)[keysOrMapper[key]];\n      },\n      set(value) {\n        return useStore(this.$pinia)[keysOrMapper[key]] = value;\n      }\n    };\n    return reduced;\n  }, {});\n}\n\n/**\n * Creates an object of references with all the state, getters, and plugin-added\n * state properties of the store. Similar to `toRefs()` but specifically\n * designed for Pinia stores so methods and non reactive properties are\n * completely ignored.\n *\n * @param store - store to extract the refs from\n */\nfunction storeToRefs(store) {\n  const rawStore = toRaw(store);\n  const refs = {};\n  for (const key in rawStore) {\n    const value = rawStore[key];\n    // There is no native method to check for a computed\n    // https://github.com/vuejs/core/pull/4165\n    if (value.effect) {\n      // @ts-expect-error: too hard to type correctly\n      refs[key] =\n      // ...\n      computed({\n        get: () => store[key],\n        set(value) {\n          store[key] = value;\n        }\n      });\n    } else if (isRef(value) || isReactive(value)) {\n      // @ts-expect-error: the key is state or getter\n      refs[key] =\n      // ---\n      toRef(store, key);\n    }\n  }\n  return refs;\n}\nexport { MutationType, acceptHMRUpdate, createPinia, defineStore, disposePinia, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, shouldHydrate, skipHydrate, storeToRefs };", "map": {"version": 3, "names": ["hasInjectionContext", "inject", "toRaw", "watch", "unref", "mark<PERSON>aw", "effectScope", "ref", "isRef", "isReactive", "getCurrentScope", "onScopeDispose", "getCurrentInstance", "reactive", "toRef", "nextTick", "computed", "toRefs", "setupDevtoolsPlugin", "activePinia", "setActivePinia", "pinia", "getActivePinia", "piniaSymbol", "process", "env", "NODE_ENV", "Symbol", "isPlainObject", "o", "Object", "prototype", "toString", "call", "toJSON", "MutationType", "IS_CLIENT", "window", "_global", "self", "global", "globalThis", "HTMLElement", "bom", "blob", "autoBom", "test", "type", "Blob", "String", "fromCharCode", "download", "url", "name", "opts", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "saveAs", "response", "onerror", "console", "error", "send", "cors<PERSON>nabled", "e", "status", "click", "node", "dispatchEvent", "MouseEvent", "evt", "bubbles", "cancelable", "view", "detail", "screenX", "screenY", "clientX", "clientY", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "button", "relatedTarget", "_navigator", "navigator", "userAgent", "isMacOSWebView", "HTMLAnchorElement", "downloadSaveAs", "msSaveAs", "fileSaverSaveAs", "a", "document", "createElement", "rel", "href", "origin", "location", "target", "URL", "createObjectURL", "setTimeout", "revokeObjectURL", "msSaveOrOpenBlob", "popup", "title", "body", "innerText", "force", "<PERSON><PERSON><PERSON><PERSON>", "isChromeIOS", "FileReader", "reader", "onloadend", "result", "Error", "replace", "assign", "readAsDataURL", "toastMessage", "message", "piniaMessage", "__VUE_DEVTOOLS_TOAST__", "warn", "log", "isPinia", "checkClipboardAccess", "checkNotFocusedError", "toLowerCase", "includes", "actionGlobalCopyState", "clipboard", "writeText", "JSON", "stringify", "state", "value", "actionGlobalPasteState", "loadStoresState", "parse", "readText", "actionGlobalSaveState", "fileInput", "getFile<PERSON><PERSON>er", "accept", "openFile", "Promise", "resolve", "reject", "onchange", "files", "file", "item", "text", "oncancel", "actionGlobalOpenStateFile", "key", "storeState", "formatDisplay", "display", "_custom", "PINIA_ROOT_LABEL", "PINIA_ROOT_ID", "formatStoreForInspectorTree", "store", "id", "label", "$id", "formatStoreForInspectorState", "storeNames", "Array", "from", "_s", "keys", "storeMap", "map", "storeId", "editable", "getters", "filter", "get", "_getters", "reduce", "$state", "length", "getterName", "_customProperties", "size", "customProperties", "formatEventData", "events", "isArray", "data", "event", "push", "operations", "oldValue", "newValue", "operation", "formatMutationType", "direct", "patchFunction", "patchObject", "isTimelineActive", "componentStateTypes", "MUTATIONS_LAYER_ID", "INSPECTOR_ID", "assign$1", "getStoreType", "registerPiniaDevtools", "app", "logo", "packageName", "homepage", "api", "now", "addTimelineLayer", "color", "addInspector", "icon", "treeFilterPlaceholder", "actions", "action", "tooltip", "sendInspectorTree", "sendInspectorState", "nodeActions", "nodeId", "$reset", "on", "inspectComponent", "payload", "proxy", "componentInstance", "_pStores", "piniaStores", "values", "for<PERSON>ach", "instanceData", "_isOptionsAPI", "getInspectorTree", "inspectorId", "stores", "concat", "rootNodes", "$pinia", "getInspectorState", "inspectedStore", "$store", "editInspectorState", "path", "has", "unshift", "set", "editComponentState", "startsWith", "addStoreToDevtools", "settings", "logStoreChanges", "defaultValue", "bind", "Date", "$onAction", "after", "onError", "args", "groupId", "runningActionId", "addTimelineEvent", "layerId", "time", "subtitle", "activeAction", "undefined", "logType", "notifyComponentUpdate", "deep", "$subscribe", "eventData", "detached", "flush", "hotUpdate", "_hotUpdate", "newStore", "info", "$dispose", "getSettings", "patchActionForGrouping", "actionNames", "wrapWithProxy", "storeActions", "actionName", "_actionId", "trackedStore", "Proxy", "Reflect", "retValue", "apply", "arguments", "devtoolsPlugin", "options", "_p", "_testing", "originalHotUpdate", "_hmrPayload", "createPinia", "scope", "run", "toBeInstalled", "install", "_a", "provide", "config", "globalProperties", "__VUE_PROD_DEVTOOLS__", "plugin", "use", "_e", "Map", "disposePinia", "stop", "clear", "splice", "isUseStore", "fn", "newState", "oldState", "subPatch", "targetValue", "acceptHMRUpdate", "initialUseStore", "hot", "newModule", "_pinia", "exportName", "useStore", "invalidate", "existingStore", "noop", "addSubscription", "subscriptions", "callback", "onCleanup", "removeSubscription", "idx", "indexOf", "triggerSubscriptions", "slice", "fallbackRunWithContext", "ACTION_MARKER", "ACTION_NAME", "mergeReactiveObjects", "patchToApply", "Set", "add", "hasOwnProperty", "skipHydrateSymbol", "skipHydrate", "obj", "defineProperty", "shouldHydrate", "isComputed", "effect", "createOptionsStore", "initialState", "setup", "localState", "computedGetters", "createSetupStore", "isOptionsStore", "optionsForPlugin", "active", "$subscribeOptions", "onTrigger", "isListening", "debuggerEvents", "_hotUpdating", "isSyncListening", "actionSubscriptions", "hotState", "activeListener", "$patch", "partialStateOrMutator", "subscriptionMutation", "myListenerId", "then", "delete", "wrappedAction", "afterCallbackList", "onErrorCallbackList", "ret", "catch", "partialStore", "stopWatcher", "runWithContext", "setupStore", "prop", "actionValue", "stateKey", "newStateTarget", "oldStateSource", "actionFn", "getter", "getterV<PERSON>ue", "nonEnumerable", "writable", "configurable", "enumerable", "p", "extender", "extensions", "constructor", "hydrate", "defineStore", "setupOptions", "isSetupStore", "hasContext", "hotId", "currentInstance", "vm", "cache", "mapStoreSuffix", "setMapStoreSuffix", "suffix", "mapStores", "reduced", "mapState", "keysOrMapper", "storeKey", "mapGetters", "mapActions", "mapWritableState", "storeToRefs", "rawStore", "refs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/new/vue-frontend-project/vue-frontend-final/node_modules/pinia/dist/pinia.mjs"], "sourcesContent": ["/*!\n * pinia v3.0.3\n * (c) 2025 <PERSON>\n * @license MIT\n */\nimport { hasInjectionContext, inject, toRaw, watch, unref, markRaw, effectScope, ref, isRef, isReactive, getCurrentScope, onScopeDispose, getCurrentInstance, reactive, toRef, nextTick, computed, toRefs } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * setActivePinia must be called to handle SSR at the top of functions like\n * `fetch`, `setup`, `serverPrefetch` and others\n */\nlet activePinia;\n/**\n * Sets or unsets the active pinia. Used in SSR and internally when calling\n * actions and getters\n *\n * @param pinia - Pinia instance\n */\n// @ts-expect-error: cannot constrain the type of the return\nconst setActivePinia = (pinia) => (activePinia = pinia);\n/**\n * Get the currently active pinia if there is any.\n */\nconst getActivePinia = () => (hasInjectionContext() && inject(piniaSymbol)) || activePinia;\nconst piniaSymbol = ((process.env.NODE_ENV !== 'production') ? Symbol('pinia') : /* istanbul ignore next */ Symbol());\n\nfunction isPlainObject(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no) {\n    return (o &&\n        typeof o === 'object' &&\n        Object.prototype.toString.call(o) === '[object Object]' &&\n        typeof o.toJSON !== 'function');\n}\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\n// TODO: can we change these to numbers?\n/**\n * Possible types for SubscriptionCallback\n */\nvar MutationType;\n(function (MutationType) {\n    /**\n     * Direct mutation of the state:\n     *\n     * - `store.name = 'new name'`\n     * - `store.$state.name = 'new name'`\n     * - `store.list.push('new item')`\n     */\n    MutationType[\"direct\"] = \"direct\";\n    /**\n     * Mutated the state with `$patch` and an object\n     *\n     * - `store.$patch({ name: 'newName' })`\n     */\n    MutationType[\"patchObject\"] = \"patch object\";\n    /**\n     * Mutated the state with `$patch` and a function\n     *\n     * - `store.$patch(state => state.name = 'newName')`\n     */\n    MutationType[\"patchFunction\"] = \"patch function\";\n    // maybe reset? for $state = {} and $reset\n})(MutationType || (MutationType = {}));\n\nconst IS_CLIENT = typeof window !== 'undefined';\n\n/*\n * FileSaver.js A saveAs() FileSaver implementation.\n *\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\n * Morote.\n *\n * License : MIT\n */\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nconst _global = /*#__PURE__*/ (() => typeof window === 'object' && window.window === window\n    ? window\n    : typeof self === 'object' && self.self === self\n        ? self\n        : typeof global === 'object' && global.global === global\n            ? global\n            : typeof globalThis === 'object'\n                ? globalThis\n                : { HTMLElement: null })();\nfunction bom(blob, { autoBom = false } = {}) {\n    // prepend BOM for UTF-8 XML and text/* types (including HTML)\n    // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n    if (autoBom &&\n        /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n        return new Blob([String.fromCharCode(0xfeff), blob], { type: blob.type });\n    }\n    return blob;\n}\nfunction download(url, name, opts) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url);\n    xhr.responseType = 'blob';\n    xhr.onload = function () {\n        saveAs(xhr.response, name, opts);\n    };\n    xhr.onerror = function () {\n        console.error('could not download file');\n    };\n    xhr.send();\n}\nfunction corsEnabled(url) {\n    const xhr = new XMLHttpRequest();\n    // use sync to avoid popup blocker\n    xhr.open('HEAD', url, false);\n    try {\n        xhr.send();\n    }\n    catch (e) { }\n    return xhr.status >= 200 && xhr.status <= 299;\n}\n// `a.click()` doesn't work for all browsers (#465)\nfunction click(node) {\n    try {\n        node.dispatchEvent(new MouseEvent('click'));\n    }\n    catch (e) {\n        const evt = new MouseEvent('click', {\n            bubbles: true,\n            cancelable: true,\n            view: window,\n            detail: 0,\n            screenX: 80,\n            screenY: 20,\n            clientX: 80,\n            clientY: 20,\n            ctrlKey: false,\n            altKey: false,\n            shiftKey: false,\n            metaKey: false,\n            button: 0,\n            relatedTarget: null,\n        });\n        node.dispatchEvent(evt);\n    }\n}\nconst _navigator = typeof navigator === 'object' ? navigator : { userAgent: '' };\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nconst isMacOSWebView = /*#__PURE__*/ (() => /Macintosh/.test(_navigator.userAgent) &&\n    /AppleWebKit/.test(_navigator.userAgent) &&\n    !/Safari/.test(_navigator.userAgent))();\nconst saveAs = !IS_CLIENT\n    ? () => { } // noop\n    : // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\n        typeof HTMLAnchorElement !== 'undefined' &&\n            'download' in HTMLAnchorElement.prototype &&\n            !isMacOSWebView\n            ? downloadSaveAs\n            : // Use msSaveOrOpenBlob as a second approach\n                'msSaveOrOpenBlob' in _navigator\n                    ? msSaveAs\n                    : // Fallback to using FileReader and a popup\n                        fileSaverSaveAs;\nfunction downloadSaveAs(blob, name = 'download', opts) {\n    const a = document.createElement('a');\n    a.download = name;\n    a.rel = 'noopener'; // tabnabbing\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n    if (typeof blob === 'string') {\n        // Support regular links\n        a.href = blob;\n        if (a.origin !== location.origin) {\n            if (corsEnabled(a.href)) {\n                download(blob, name, opts);\n            }\n            else {\n                a.target = '_blank';\n                click(a);\n            }\n        }\n        else {\n            click(a);\n        }\n    }\n    else {\n        // Support blobs\n        a.href = URL.createObjectURL(blob);\n        setTimeout(function () {\n            URL.revokeObjectURL(a.href);\n        }, 4e4); // 40s\n        setTimeout(function () {\n            click(a);\n        }, 0);\n    }\n}\nfunction msSaveAs(blob, name = 'download', opts) {\n    if (typeof blob === 'string') {\n        if (corsEnabled(blob)) {\n            download(blob, name, opts);\n        }\n        else {\n            const a = document.createElement('a');\n            a.href = blob;\n            a.target = '_blank';\n            setTimeout(function () {\n                click(a);\n            });\n        }\n    }\n    else {\n        // @ts-ignore: works on windows\n        navigator.msSaveOrOpenBlob(bom(blob, opts), name);\n    }\n}\nfunction fileSaverSaveAs(blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank');\n    if (popup) {\n        popup.document.title = popup.document.body.innerText = 'downloading...';\n    }\n    if (typeof blob === 'string')\n        return download(blob, name, opts);\n    const force = blob.type === 'application/octet-stream';\n    const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\n    const isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) &&\n        typeof FileReader !== 'undefined') {\n        // Safari doesn't allow downloading of blob URLs\n        const reader = new FileReader();\n        reader.onloadend = function () {\n            let url = reader.result;\n            if (typeof url !== 'string') {\n                popup = null;\n                throw new Error('Wrong reader.result type');\n            }\n            url = isChromeIOS\n                ? url\n                : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\n            if (popup) {\n                popup.location.href = url;\n            }\n            else {\n                location.assign(url);\n            }\n            popup = null; // reverse-tabnabbing #460\n        };\n        reader.readAsDataURL(blob);\n    }\n    else {\n        const url = URL.createObjectURL(blob);\n        if (popup)\n            popup.location.assign(url);\n        else\n            location.href = url;\n        popup = null; // reverse-tabnabbing #460\n        setTimeout(function () {\n            URL.revokeObjectURL(url);\n        }, 4e4); // 40s\n    }\n}\n\n/**\n * Shows a toast or console.log\n *\n * @param message - message to log\n * @param type - different color of the tooltip\n */\nfunction toastMessage(message, type) {\n    const piniaMessage = '🍍 ' + message;\n    if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\n        // No longer available :(\n        __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\n    }\n    else if (type === 'error') {\n        console.error(piniaMessage);\n    }\n    else if (type === 'warn') {\n        console.warn(piniaMessage);\n    }\n    else {\n        console.log(piniaMessage);\n    }\n}\nfunction isPinia(o) {\n    return '_a' in o && 'install' in o;\n}\n\n/**\n * This file contain devtools actions, they are not Pinia actions.\n */\n// ---\nfunction checkClipboardAccess() {\n    if (!('clipboard' in navigator)) {\n        toastMessage(`Your browser doesn't support the Clipboard API`, 'error');\n        return true;\n    }\n}\nfunction checkNotFocusedError(error) {\n    if (error instanceof Error &&\n        error.message.toLowerCase().includes('document is not focused')) {\n        toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\n        return true;\n    }\n    return false;\n}\nasync function actionGlobalCopyState(pinia) {\n    if (checkClipboardAccess())\n        return;\n    try {\n        await navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\n        toastMessage('Global state copied to clipboard.');\n    }\n    catch (error) {\n        if (checkNotFocusedError(error))\n            return;\n        toastMessage(`Failed to serialize the state. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nasync function actionGlobalPasteState(pinia) {\n    if (checkClipboardAccess())\n        return;\n    try {\n        loadStoresState(pinia, JSON.parse(await navigator.clipboard.readText()));\n        toastMessage('Global state pasted from clipboard.');\n    }\n    catch (error) {\n        if (checkNotFocusedError(error))\n            return;\n        toastMessage(`Failed to deserialize the state from clipboard. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nasync function actionGlobalSaveState(pinia) {\n    try {\n        saveAs(new Blob([JSON.stringify(pinia.state.value)], {\n            type: 'text/plain;charset=utf-8',\n        }), 'pinia-state.json');\n    }\n    catch (error) {\n        toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nlet fileInput;\nfunction getFileOpener() {\n    if (!fileInput) {\n        fileInput = document.createElement('input');\n        fileInput.type = 'file';\n        fileInput.accept = '.json';\n    }\n    function openFile() {\n        return new Promise((resolve, reject) => {\n            fileInput.onchange = async () => {\n                const files = fileInput.files;\n                if (!files)\n                    return resolve(null);\n                const file = files.item(0);\n                if (!file)\n                    return resolve(null);\n                return resolve({ text: await file.text(), file });\n            };\n            // @ts-ignore: TODO: changed from 4.3 to 4.4\n            fileInput.oncancel = () => resolve(null);\n            fileInput.onerror = reject;\n            fileInput.click();\n        });\n    }\n    return openFile;\n}\nasync function actionGlobalOpenStateFile(pinia) {\n    try {\n        const open = getFileOpener();\n        const result = await open();\n        if (!result)\n            return;\n        const { text, file } = result;\n        loadStoresState(pinia, JSON.parse(text));\n        toastMessage(`Global state imported from \"${file.name}\".`);\n    }\n    catch (error) {\n        toastMessage(`Failed to import the state from JSON. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nfunction loadStoresState(pinia, state) {\n    for (const key in state) {\n        const storeState = pinia.state.value[key];\n        // store is already instantiated, patch it\n        if (storeState) {\n            Object.assign(storeState, state[key]);\n        }\n        else {\n            // store is not instantiated, set the initial state\n            pinia.state.value[key] = state[key];\n        }\n    }\n}\n\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\nconst PINIA_ROOT_LABEL = '🍍 Pinia (root)';\nconst PINIA_ROOT_ID = '_root';\nfunction formatStoreForInspectorTree(store) {\n    return isPinia(store)\n        ? {\n            id: PINIA_ROOT_ID,\n            label: PINIA_ROOT_LABEL,\n        }\n        : {\n            id: store.$id,\n            label: store.$id,\n        };\n}\nfunction formatStoreForInspectorState(store) {\n    if (isPinia(store)) {\n        const storeNames = Array.from(store._s.keys());\n        const storeMap = store._s;\n        const state = {\n            state: storeNames.map((storeId) => ({\n                editable: true,\n                key: storeId,\n                value: store.state.value[storeId],\n            })),\n            getters: storeNames\n                .filter((id) => storeMap.get(id)._getters)\n                .map((id) => {\n                const store = storeMap.get(id);\n                return {\n                    editable: false,\n                    key: id,\n                    value: store._getters.reduce((getters, key) => {\n                        getters[key] = store[key];\n                        return getters;\n                    }, {}),\n                };\n            }),\n        };\n        return state;\n    }\n    const state = {\n        state: Object.keys(store.$state).map((key) => ({\n            editable: true,\n            key,\n            value: store.$state[key],\n        })),\n    };\n    // avoid adding empty getters\n    if (store._getters && store._getters.length) {\n        state.getters = store._getters.map((getterName) => ({\n            editable: false,\n            key: getterName,\n            value: store[getterName],\n        }));\n    }\n    if (store._customProperties.size) {\n        state.customProperties = Array.from(store._customProperties).map((key) => ({\n            editable: true,\n            key,\n            value: store[key],\n        }));\n    }\n    return state;\n}\nfunction formatEventData(events) {\n    if (!events)\n        return {};\n    if (Array.isArray(events)) {\n        // TODO: handle add and delete for arrays and objects\n        return events.reduce((data, event) => {\n            data.keys.push(event.key);\n            data.operations.push(event.type);\n            data.oldValue[event.key] = event.oldValue;\n            data.newValue[event.key] = event.newValue;\n            return data;\n        }, {\n            oldValue: {},\n            keys: [],\n            operations: [],\n            newValue: {},\n        });\n    }\n    else {\n        return {\n            operation: formatDisplay(events.type),\n            key: formatDisplay(events.key),\n            oldValue: events.oldValue,\n            newValue: events.newValue,\n        };\n    }\n}\nfunction formatMutationType(type) {\n    switch (type) {\n        case MutationType.direct:\n            return 'mutation';\n        case MutationType.patchFunction:\n            return '$patch';\n        case MutationType.patchObject:\n            return '$patch';\n        default:\n            return 'unknown';\n    }\n}\n\n// timeline can be paused when directly changing the state\nlet isTimelineActive = true;\nconst componentStateTypes = [];\nconst MUTATIONS_LAYER_ID = 'pinia:mutations';\nconst INSPECTOR_ID = 'pinia';\nconst { assign: assign$1 } = Object;\n/**\n * Gets the displayed name of a store in devtools\n *\n * @param id - id of the store\n * @returns a formatted string\n */\nconst getStoreType = (id) => '🍍 ' + id;\n/**\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\n * as soon as it is added to the application.\n *\n * @param app - Vue application\n * @param pinia - pinia instance\n */\nfunction registerPiniaDevtools(app, pinia) {\n    setupDevtoolsPlugin({\n        id: 'dev.esm.pinia',\n        label: 'Pinia 🍍',\n        logo: 'https://pinia.vuejs.org/logo.svg',\n        packageName: 'pinia',\n        homepage: 'https://pinia.vuejs.org',\n        componentStateTypes,\n        app,\n    }, (api) => {\n        if (typeof api.now !== 'function') {\n            toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        api.addTimelineLayer({\n            id: MUTATIONS_LAYER_ID,\n            label: `Pinia 🍍`,\n            color: 0xe5df88,\n        });\n        api.addInspector({\n            id: INSPECTOR_ID,\n            label: 'Pinia 🍍',\n            icon: 'storage',\n            treeFilterPlaceholder: 'Search stores',\n            actions: [\n                {\n                    icon: 'content_copy',\n                    action: () => {\n                        actionGlobalCopyState(pinia);\n                    },\n                    tooltip: 'Serialize and copy the state',\n                },\n                {\n                    icon: 'content_paste',\n                    action: async () => {\n                        await actionGlobalPasteState(pinia);\n                        api.sendInspectorTree(INSPECTOR_ID);\n                        api.sendInspectorState(INSPECTOR_ID);\n                    },\n                    tooltip: 'Replace the state with the content of your clipboard',\n                },\n                {\n                    icon: 'save',\n                    action: () => {\n                        actionGlobalSaveState(pinia);\n                    },\n                    tooltip: 'Save the state as a JSON file',\n                },\n                {\n                    icon: 'folder_open',\n                    action: async () => {\n                        await actionGlobalOpenStateFile(pinia);\n                        api.sendInspectorTree(INSPECTOR_ID);\n                        api.sendInspectorState(INSPECTOR_ID);\n                    },\n                    tooltip: 'Import the state from a JSON file',\n                },\n            ],\n            nodeActions: [\n                {\n                    icon: 'restore',\n                    tooltip: 'Reset the state (with \"$reset\")',\n                    action: (nodeId) => {\n                        const store = pinia._s.get(nodeId);\n                        if (!store) {\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it wasn't found.`, 'warn');\n                        }\n                        else if (typeof store.$reset !== 'function') {\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it doesn't have a \"$reset\" method implemented.`, 'warn');\n                        }\n                        else {\n                            store.$reset();\n                            toastMessage(`Store \"${nodeId}\" reset.`);\n                        }\n                    },\n                },\n            ],\n        });\n        api.on.inspectComponent((payload) => {\n            const proxy = (payload.componentInstance &&\n                payload.componentInstance.proxy);\n            if (proxy && proxy._pStores) {\n                const piniaStores = payload.componentInstance.proxy._pStores;\n                Object.values(piniaStores).forEach((store) => {\n                    payload.instanceData.state.push({\n                        type: getStoreType(store.$id),\n                        key: 'state',\n                        editable: true,\n                        value: store._isOptionsAPI\n                            ? {\n                                _custom: {\n                                    value: toRaw(store.$state),\n                                    actions: [\n                                        {\n                                            icon: 'restore',\n                                            tooltip: 'Reset the state of this store',\n                                            action: () => store.$reset(),\n                                        },\n                                    ],\n                                },\n                            }\n                            : // NOTE: workaround to unwrap transferred refs\n                                Object.keys(store.$state).reduce((state, key) => {\n                                    state[key] = store.$state[key];\n                                    return state;\n                                }, {}),\n                    });\n                    if (store._getters && store._getters.length) {\n                        payload.instanceData.state.push({\n                            type: getStoreType(store.$id),\n                            key: 'getters',\n                            editable: false,\n                            value: store._getters.reduce((getters, key) => {\n                                try {\n                                    getters[key] = store[key];\n                                }\n                                catch (error) {\n                                    // @ts-expect-error: we just want to show it in devtools\n                                    getters[key] = error;\n                                }\n                                return getters;\n                            }, {}),\n                        });\n                    }\n                });\n            }\n        });\n        api.on.getInspectorTree((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                let stores = [pinia];\n                stores = stores.concat(Array.from(pinia._s.values()));\n                payload.rootNodes = (payload.filter\n                    ? stores.filter((store) => '$id' in store\n                        ? store.$id\n                            .toLowerCase()\n                            .includes(payload.filter.toLowerCase())\n                        : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase()))\n                    : stores).map(formatStoreForInspectorTree);\n            }\n        });\n        // Expose pinia instance as $pinia to window\n        globalThis.$pinia = pinia;\n        api.on.getInspectorState((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\n                    ? pinia\n                    : pinia._s.get(payload.nodeId);\n                if (!inspectedStore) {\n                    // this could be the selected store restored for a different project\n                    // so it's better not to say anything here\n                    return;\n                }\n                if (inspectedStore) {\n                    // Expose selected store as $store to window\n                    if (payload.nodeId !== PINIA_ROOT_ID)\n                        globalThis.$store = toRaw(inspectedStore);\n                    payload.state = formatStoreForInspectorState(inspectedStore);\n                }\n            }\n        });\n        api.on.editInspectorState((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\n                    ? pinia\n                    : pinia._s.get(payload.nodeId);\n                if (!inspectedStore) {\n                    return toastMessage(`store \"${payload.nodeId}\" not found`, 'error');\n                }\n                const { path } = payload;\n                if (!isPinia(inspectedStore)) {\n                    // access only the state\n                    if (path.length !== 1 ||\n                        !inspectedStore._customProperties.has(path[0]) ||\n                        path[0] in inspectedStore.$state) {\n                        path.unshift('$state');\n                    }\n                }\n                else {\n                    // Root access, we can omit the `.value` because the devtools API does it for us\n                    path.unshift('state');\n                }\n                isTimelineActive = false;\n                payload.set(inspectedStore, path, payload.state.value);\n                isTimelineActive = true;\n            }\n        });\n        api.on.editComponentState((payload) => {\n            if (payload.type.startsWith('🍍')) {\n                const storeId = payload.type.replace(/^🍍\\s*/, '');\n                const store = pinia._s.get(storeId);\n                if (!store) {\n                    return toastMessage(`store \"${storeId}\" not found`, 'error');\n                }\n                const { path } = payload;\n                if (path[0] !== 'state') {\n                    return toastMessage(`Invalid path for store \"${storeId}\":\\n${path}\\nOnly state can be modified.`);\n                }\n                // rewrite the first entry to be able to directly set the state as\n                // well as any other path\n                path[0] = '$state';\n                isTimelineActive = false;\n                payload.set(store, path, payload.state.value);\n                isTimelineActive = true;\n            }\n        });\n    });\n}\nfunction addStoreToDevtools(app, store) {\n    if (!componentStateTypes.includes(getStoreType(store.$id))) {\n        componentStateTypes.push(getStoreType(store.$id));\n    }\n    setupDevtoolsPlugin({\n        id: 'dev.esm.pinia',\n        label: 'Pinia 🍍',\n        logo: 'https://pinia.vuejs.org/logo.svg',\n        packageName: 'pinia',\n        homepage: 'https://pinia.vuejs.org',\n        componentStateTypes,\n        app,\n        settings: {\n            logStoreChanges: {\n                label: 'Notify about new/deleted stores',\n                type: 'boolean',\n                defaultValue: true,\n            },\n            // useEmojis: {\n            //   label: 'Use emojis in messages ⚡️',\n            //   type: 'boolean',\n            //   defaultValue: true,\n            // },\n        },\n    }, (api) => {\n        // gracefully handle errors\n        const now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\n        store.$onAction(({ after, onError, name, args }) => {\n            const groupId = runningActionId++;\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: {\n                    time: now(),\n                    title: '🛫 ' + name,\n                    subtitle: 'start',\n                    data: {\n                        store: formatDisplay(store.$id),\n                        action: formatDisplay(name),\n                        args,\n                    },\n                    groupId,\n                },\n            });\n            after((result) => {\n                activeAction = undefined;\n                api.addTimelineEvent({\n                    layerId: MUTATIONS_LAYER_ID,\n                    event: {\n                        time: now(),\n                        title: '🛬 ' + name,\n                        subtitle: 'end',\n                        data: {\n                            store: formatDisplay(store.$id),\n                            action: formatDisplay(name),\n                            args,\n                            result,\n                        },\n                        groupId,\n                    },\n                });\n            });\n            onError((error) => {\n                activeAction = undefined;\n                api.addTimelineEvent({\n                    layerId: MUTATIONS_LAYER_ID,\n                    event: {\n                        time: now(),\n                        logType: 'error',\n                        title: '💥 ' + name,\n                        subtitle: 'end',\n                        data: {\n                            store: formatDisplay(store.$id),\n                            action: formatDisplay(name),\n                            args,\n                            error,\n                        },\n                        groupId,\n                    },\n                });\n            });\n        }, true);\n        store._customProperties.forEach((name) => {\n            watch(() => unref(store[name]), (newValue, oldValue) => {\n                api.notifyComponentUpdate();\n                api.sendInspectorState(INSPECTOR_ID);\n                if (isTimelineActive) {\n                    api.addTimelineEvent({\n                        layerId: MUTATIONS_LAYER_ID,\n                        event: {\n                            time: now(),\n                            title: 'Change',\n                            subtitle: name,\n                            data: {\n                                newValue,\n                                oldValue,\n                            },\n                            groupId: activeAction,\n                        },\n                    });\n                }\n            }, { deep: true });\n        });\n        store.$subscribe(({ events, type }, state) => {\n            api.notifyComponentUpdate();\n            api.sendInspectorState(INSPECTOR_ID);\n            if (!isTimelineActive)\n                return;\n            // rootStore.state[store.id] = state\n            const eventData = {\n                time: now(),\n                title: formatMutationType(type),\n                data: assign$1({ store: formatDisplay(store.$id) }, formatEventData(events)),\n                groupId: activeAction,\n            };\n            if (type === MutationType.patchFunction) {\n                eventData.subtitle = '⤵️';\n            }\n            else if (type === MutationType.patchObject) {\n                eventData.subtitle = '🧩';\n            }\n            else if (events && !Array.isArray(events)) {\n                eventData.subtitle = events.type;\n            }\n            if (events) {\n                eventData.data['rawEvent(s)'] = {\n                    _custom: {\n                        display: 'DebuggerEvent',\n                        type: 'object',\n                        tooltip: 'raw DebuggerEvent[]',\n                        value: events,\n                    },\n                };\n            }\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: eventData,\n            });\n        }, { detached: true, flush: 'sync' });\n        const hotUpdate = store._hotUpdate;\n        store._hotUpdate = markRaw((newStore) => {\n            hotUpdate(newStore);\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: {\n                    time: now(),\n                    title: '🔥 ' + store.$id,\n                    subtitle: 'HMR update',\n                    data: {\n                        store: formatDisplay(store.$id),\n                        info: formatDisplay(`HMR update`),\n                    },\n                },\n            });\n            // update the devtools too\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(INSPECTOR_ID);\n            api.sendInspectorState(INSPECTOR_ID);\n        });\n        const { $dispose } = store;\n        store.$dispose = () => {\n            $dispose();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(INSPECTOR_ID);\n            api.sendInspectorState(INSPECTOR_ID);\n            api.getSettings().logStoreChanges &&\n                toastMessage(`Disposed \"${store.$id}\" store 🗑`);\n        };\n        // trigger an update so it can display new registered stores\n        api.notifyComponentUpdate();\n        api.sendInspectorTree(INSPECTOR_ID);\n        api.sendInspectorState(INSPECTOR_ID);\n        api.getSettings().logStoreChanges &&\n            toastMessage(`\"${store.$id}\" store installed 🆕`);\n    });\n}\nlet runningActionId = 0;\nlet activeAction;\n/**\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\n * mutation to the action.\n *\n * @param store - store to patch\n * @param actionNames - list of actionst to patch\n */\nfunction patchActionForGrouping(store, actionNames, wrapWithProxy) {\n    // original actions of the store as they are given by pinia. We are going to override them\n    const actions = actionNames.reduce((storeActions, actionName) => {\n        // use toRaw to avoid tracking #541\n        storeActions[actionName] = toRaw(store)[actionName];\n        return storeActions;\n    }, {});\n    for (const actionName in actions) {\n        store[actionName] = function () {\n            // the running action id is incremented in a before action hook\n            const _actionId = runningActionId;\n            const trackedStore = wrapWithProxy\n                ? new Proxy(store, {\n                    get(...args) {\n                        activeAction = _actionId;\n                        return Reflect.get(...args);\n                    },\n                    set(...args) {\n                        activeAction = _actionId;\n                        return Reflect.set(...args);\n                    },\n                })\n                : store;\n            // For Setup Stores we need https://github.com/tc39/proposal-async-context\n            activeAction = _actionId;\n            const retValue = actions[actionName].apply(trackedStore, arguments);\n            // this is safer as async actions in Setup Stores would associate mutations done outside of the action\n            activeAction = undefined;\n            return retValue;\n        };\n    }\n}\n/**\n * pinia.use(devtoolsPlugin)\n */\nfunction devtoolsPlugin({ app, store, options }) {\n    // HMR module\n    if (store.$id.startsWith('__hot:')) {\n        return;\n    }\n    // detect option api vs setup api\n    store._isOptionsAPI = !!options.state;\n    // Do not overwrite actions mocked by @pinia/testing (#2298)\n    if (!store._p._testing) {\n        patchActionForGrouping(store, Object.keys(options.actions), store._isOptionsAPI);\n        // Upgrade the HMR to also update the new actions\n        const originalHotUpdate = store._hotUpdate;\n        toRaw(store)._hotUpdate = function (newStore) {\n            originalHotUpdate.apply(this, arguments);\n            patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions), !!store._isOptionsAPI);\n        };\n    }\n    addStoreToDevtools(app, \n    // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\n    store);\n}\n\n/**\n * Creates a Pinia instance to be used by the application\n */\nfunction createPinia() {\n    const scope = effectScope(true);\n    // NOTE: here we could check the window object for a state and directly set it\n    // if there is anything like it with Vue 3 SSR\n    const state = scope.run(() => ref({}));\n    let _p = [];\n    // plugins added before calling app.use(pinia)\n    let toBeInstalled = [];\n    const pinia = markRaw({\n        install(app) {\n            // this allows calling useStore() outside of a component setup after\n            // installing pinia's plugin\n            setActivePinia(pinia);\n            pinia._a = app;\n            app.provide(piniaSymbol, pinia);\n            app.config.globalProperties.$pinia = pinia;\n            /* istanbul ignore else */\n            if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n                registerPiniaDevtools(app, pinia);\n            }\n            toBeInstalled.forEach((plugin) => _p.push(plugin));\n            toBeInstalled = [];\n        },\n        use(plugin) {\n            if (!this._a) {\n                toBeInstalled.push(plugin);\n            }\n            else {\n                _p.push(plugin);\n            }\n            return this;\n        },\n        _p,\n        // it's actually undefined here\n        // @ts-expect-error\n        _a: null,\n        _e: scope,\n        _s: new Map(),\n        state,\n    });\n    // pinia devtools rely on dev only features so they cannot be forced unless\n    // the dev build of Vue is used. Avoid old browsers like IE11.\n    if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT && typeof Proxy !== 'undefined') {\n        pinia.use(devtoolsPlugin);\n    }\n    return pinia;\n}\n/**\n * Dispose a Pinia instance by stopping its effectScope and removing the state, plugins and stores. This is mostly\n * useful in tests, with both a testing pinia or a regular pinia and in applications that use multiple pinia instances.\n * Once disposed, the pinia instance cannot be used anymore.\n *\n * @param pinia - pinia instance\n */\nfunction disposePinia(pinia) {\n    pinia._e.stop();\n    pinia._s.clear();\n    pinia._p.splice(0);\n    pinia.state.value = {};\n    // @ts-expect-error: non valid\n    pinia._a = null;\n}\n\n/**\n * Checks if a function is a `StoreDefinition`.\n *\n * @param fn - object to test\n * @returns true if `fn` is a StoreDefinition\n */\nconst isUseStore = (fn) => {\n    return typeof fn === 'function' && typeof fn.$id === 'string';\n};\n/**\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\n * remove any key not existing in `newState` and recursively merge plain\n * objects.\n *\n * @param newState - new state object to be patched\n * @param oldState - old state that should be used to patch newState\n * @returns - newState\n */\nfunction patchObject(newState, oldState) {\n    // no need to go through symbols because they cannot be serialized anyway\n    for (const key in oldState) {\n        const subPatch = oldState[key];\n        // skip the whole sub tree\n        if (!(key in newState)) {\n            continue;\n        }\n        const targetValue = newState[key];\n        if (isPlainObject(targetValue) &&\n            isPlainObject(subPatch) &&\n            !isRef(subPatch) &&\n            !isReactive(subPatch)) {\n            newState[key] = patchObject(targetValue, subPatch);\n        }\n        else {\n            // objects are either a bit more complex (e.g. refs) or primitives, so we\n            // just set the whole thing\n            newState[key] = subPatch;\n        }\n    }\n    return newState;\n}\n/**\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\n *\n * @example\n * ```js\n * const useUser = defineStore(...)\n * if (import.meta.hot) {\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\n * }\n * ```\n *\n * @param initialUseStore - return of the defineStore to hot update\n * @param hot - `import.meta.hot`\n */\nfunction acceptHMRUpdate(initialUseStore, hot) {\n    // strip as much as possible from iife.prod\n    if (!(process.env.NODE_ENV !== 'production')) {\n        return () => { };\n    }\n    return (newModule) => {\n        const pinia = hot.data.pinia || initialUseStore._pinia;\n        if (!pinia) {\n            // this store is still not used\n            return;\n        }\n        // preserve the pinia instance across loads\n        hot.data.pinia = pinia;\n        // console.log('got data', newStore)\n        for (const exportName in newModule) {\n            const useStore = newModule[exportName];\n            // console.log('checking for', exportName)\n            if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\n                // console.log('Accepting update for', useStore.$id)\n                const id = useStore.$id;\n                if (id !== initialUseStore.$id) {\n                    console.warn(`The id of the store changed from \"${initialUseStore.$id}\" to \"${id}\". Reloading.`);\n                    // return import.meta.hot.invalidate()\n                    return hot.invalidate();\n                }\n                const existingStore = pinia._s.get(id);\n                if (!existingStore) {\n                    console.log(`[Pinia]: skipping hmr because store doesn't exist yet`);\n                    return;\n                }\n                useStore(pinia, existingStore);\n            }\n        }\n    };\n}\n\nconst noop = () => { };\nfunction addSubscription(subscriptions, callback, detached, onCleanup = noop) {\n    subscriptions.push(callback);\n    const removeSubscription = () => {\n        const idx = subscriptions.indexOf(callback);\n        if (idx > -1) {\n            subscriptions.splice(idx, 1);\n            onCleanup();\n        }\n    };\n    if (!detached && getCurrentScope()) {\n        onScopeDispose(removeSubscription);\n    }\n    return removeSubscription;\n}\nfunction triggerSubscriptions(subscriptions, ...args) {\n    subscriptions.slice().forEach((callback) => {\n        callback(...args);\n    });\n}\n\nconst fallbackRunWithContext = (fn) => fn();\n/**\n * Marks a function as an action for `$onAction`\n * @internal\n */\nconst ACTION_MARKER = Symbol();\n/**\n * Action name symbol. Allows to add a name to an action after defining it\n * @internal\n */\nconst ACTION_NAME = Symbol();\nfunction mergeReactiveObjects(target, patchToApply) {\n    // Handle Map instances\n    if (target instanceof Map && patchToApply instanceof Map) {\n        patchToApply.forEach((value, key) => target.set(key, value));\n    }\n    else if (target instanceof Set && patchToApply instanceof Set) {\n        // Handle Set instances\n        patchToApply.forEach(target.add, target);\n    }\n    // no need to go through symbols because they cannot be serialized anyway\n    for (const key in patchToApply) {\n        if (!patchToApply.hasOwnProperty(key))\n            continue;\n        const subPatch = patchToApply[key];\n        const targetValue = target[key];\n        if (isPlainObject(targetValue) &&\n            isPlainObject(subPatch) &&\n            target.hasOwnProperty(key) &&\n            !isRef(subPatch) &&\n            !isReactive(subPatch)) {\n            // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\n            // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\n            // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\n            target[key] = mergeReactiveObjects(targetValue, subPatch);\n        }\n        else {\n            // @ts-expect-error: subPatch is a valid value\n            target[key] = subPatch;\n        }\n    }\n    return target;\n}\nconst skipHydrateSymbol = (process.env.NODE_ENV !== 'production')\n    ? Symbol('pinia:skipHydration')\n    : /* istanbul ignore next */ Symbol();\n/**\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\n *\n * @param obj - target object\n * @returns obj\n */\nfunction skipHydrate(obj) {\n    return Object.defineProperty(obj, skipHydrateSymbol, {});\n}\n/**\n * Returns whether a value should be hydrated\n *\n * @param obj - target variable\n * @returns true if `obj` should be hydrated\n */\nfunction shouldHydrate(obj) {\n    return (!isPlainObject(obj) ||\n        !Object.prototype.hasOwnProperty.call(obj, skipHydrateSymbol));\n}\nconst { assign } = Object;\nfunction isComputed(o) {\n    return !!(isRef(o) && o.effect);\n}\nfunction createOptionsStore(id, options, pinia, hot) {\n    const { state, actions, getters } = options;\n    const initialState = pinia.state.value[id];\n    let store;\n    function setup() {\n        if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n            /* istanbul ignore if */\n            pinia.state.value[id] = state ? state() : {};\n        }\n        // avoid creating a state in pinia.state.value\n        const localState = (process.env.NODE_ENV !== 'production') && hot\n            ? // use ref() to unwrap refs inside state TODO: check if this is still necessary\n                toRefs(ref(state ? state() : {}).value)\n            : toRefs(pinia.state.value[id]);\n        return assign(localState, actions, Object.keys(getters || {}).reduce((computedGetters, name) => {\n            if ((process.env.NODE_ENV !== 'production') && name in localState) {\n                console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with \"${name}\" in store \"${id}\".`);\n            }\n            computedGetters[name] = markRaw(computed(() => {\n                setActivePinia(pinia);\n                // it was created just before\n                const store = pinia._s.get(id);\n                // allow cross using stores\n                // @ts-expect-error\n                // return getters![name].call(context, context)\n                // TODO: avoid reading the getter while assigning with a global variable\n                return getters[name].call(store, store);\n            }));\n            return computedGetters;\n        }, {}));\n    }\n    store = createSetupStore(id, setup, options, pinia, hot, true);\n    return store;\n}\nfunction createSetupStore($id, setup, options = {}, pinia, hot, isOptionsStore) {\n    let scope;\n    const optionsForPlugin = assign({ actions: {} }, options);\n    /* istanbul ignore if */\n    if ((process.env.NODE_ENV !== 'production') && !pinia._e.active) {\n        throw new Error('Pinia destroyed');\n    }\n    // watcher options for $subscribe\n    const $subscribeOptions = { deep: true };\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production')) {\n        $subscribeOptions.onTrigger = (event) => {\n            /* istanbul ignore else */\n            if (isListening) {\n                debuggerEvents = event;\n                // avoid triggering this while the store is being built and the state is being set in pinia\n            }\n            else if (isListening == false && !store._hotUpdating) {\n                // let patch send all the events together later\n                /* istanbul ignore else */\n                if (Array.isArray(debuggerEvents)) {\n                    debuggerEvents.push(event);\n                }\n                else {\n                    console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\n                }\n            }\n        };\n    }\n    // internal state\n    let isListening; // set to true at the end\n    let isSyncListening; // set to true at the end\n    let subscriptions = [];\n    let actionSubscriptions = [];\n    let debuggerEvents;\n    const initialState = pinia.state.value[$id];\n    // avoid setting the state for option stores if it is set\n    // by the setup\n    if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n        /* istanbul ignore if */\n        pinia.state.value[$id] = {};\n    }\n    const hotState = ref({});\n    // avoid triggering too many listeners\n    // https://github.com/vuejs/pinia/issues/1129\n    let activeListener;\n    function $patch(partialStateOrMutator) {\n        let subscriptionMutation;\n        isListening = isSyncListening = false;\n        // reset the debugger events since patches are sync\n        /* istanbul ignore else */\n        if ((process.env.NODE_ENV !== 'production')) {\n            debuggerEvents = [];\n        }\n        if (typeof partialStateOrMutator === 'function') {\n            partialStateOrMutator(pinia.state.value[$id]);\n            subscriptionMutation = {\n                type: MutationType.patchFunction,\n                storeId: $id,\n                events: debuggerEvents,\n            };\n        }\n        else {\n            mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\n            subscriptionMutation = {\n                type: MutationType.patchObject,\n                payload: partialStateOrMutator,\n                storeId: $id,\n                events: debuggerEvents,\n            };\n        }\n        const myListenerId = (activeListener = Symbol());\n        nextTick().then(() => {\n            if (activeListener === myListenerId) {\n                isListening = true;\n            }\n        });\n        isSyncListening = true;\n        // because we paused the watcher, we need to manually call the subscriptions\n        triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\n    }\n    const $reset = isOptionsStore\n        ? function $reset() {\n            const { state } = options;\n            const newState = state ? state() : {};\n            // we use a patch to group all changes into one single subscription\n            this.$patch(($state) => {\n                // @ts-expect-error: FIXME: shouldn't error?\n                assign($state, newState);\n            });\n        }\n        : /* istanbul ignore next */\n            (process.env.NODE_ENV !== 'production')\n                ? () => {\n                    throw new Error(`🍍: Store \"${$id}\" is built using the setup syntax and does not implement $reset().`);\n                }\n                : noop;\n    function $dispose() {\n        scope.stop();\n        subscriptions = [];\n        actionSubscriptions = [];\n        pinia._s.delete($id);\n    }\n    /**\n     * Helper that wraps function so it can be tracked with $onAction\n     * @param fn - action to wrap\n     * @param name - name of the action\n     */\n    const action = (fn, name = '') => {\n        if (ACTION_MARKER in fn) {\n            fn[ACTION_NAME] = name;\n            return fn;\n        }\n        const wrappedAction = function () {\n            setActivePinia(pinia);\n            const args = Array.from(arguments);\n            const afterCallbackList = [];\n            const onErrorCallbackList = [];\n            function after(callback) {\n                afterCallbackList.push(callback);\n            }\n            function onError(callback) {\n                onErrorCallbackList.push(callback);\n            }\n            // @ts-expect-error\n            triggerSubscriptions(actionSubscriptions, {\n                args,\n                name: wrappedAction[ACTION_NAME],\n                store,\n                after,\n                onError,\n            });\n            let ret;\n            try {\n                ret = fn.apply(this && this.$id === $id ? this : store, args);\n                // handle sync errors\n            }\n            catch (error) {\n                triggerSubscriptions(onErrorCallbackList, error);\n                throw error;\n            }\n            if (ret instanceof Promise) {\n                return ret\n                    .then((value) => {\n                    triggerSubscriptions(afterCallbackList, value);\n                    return value;\n                })\n                    .catch((error) => {\n                    triggerSubscriptions(onErrorCallbackList, error);\n                    return Promise.reject(error);\n                });\n            }\n            // trigger after callbacks\n            triggerSubscriptions(afterCallbackList, ret);\n            return ret;\n        };\n        wrappedAction[ACTION_MARKER] = true;\n        wrappedAction[ACTION_NAME] = name; // will be set later\n        // @ts-expect-error: we are intentionally limiting the returned type to just Fn\n        // because all the added properties are internals that are exposed through `$onAction()` only\n        return wrappedAction;\n    };\n    const _hmrPayload = /*#__PURE__*/ markRaw({\n        actions: {},\n        getters: {},\n        state: [],\n        hotState,\n    });\n    const partialStore = {\n        _p: pinia,\n        // _s: scope,\n        $id,\n        $onAction: addSubscription.bind(null, actionSubscriptions),\n        $patch,\n        $reset,\n        $subscribe(callback, options = {}) {\n            const removeSubscription = addSubscription(subscriptions, callback, options.detached, () => stopWatcher());\n            const stopWatcher = scope.run(() => watch(() => pinia.state.value[$id], (state) => {\n                if (options.flush === 'sync' ? isSyncListening : isListening) {\n                    callback({\n                        storeId: $id,\n                        type: MutationType.direct,\n                        events: debuggerEvents,\n                    }, state);\n                }\n            }, assign({}, $subscribeOptions, options)));\n            return removeSubscription;\n        },\n        $dispose,\n    };\n    const store = reactive((process.env.NODE_ENV !== 'production') || ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT)\n        ? assign({\n            _hmrPayload,\n            _customProperties: markRaw(new Set()), // devtools custom properties\n        }, partialStore\n        // must be added later\n        // setupStore\n        )\n        : partialStore);\n    // store the partial store now so the setup of stores can instantiate each other before they are finished without\n    // creating infinite loops.\n    pinia._s.set($id, store);\n    const runWithContext = (pinia._a && pinia._a.runWithContext) || fallbackRunWithContext;\n    // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\n    const setupStore = runWithContext(() => pinia._e.run(() => (scope = effectScope()).run(() => setup({ action }))));\n    // overwrite existing actions to support $onAction\n    for (const key in setupStore) {\n        const prop = setupStore[key];\n        if ((isRef(prop) && !isComputed(prop)) || isReactive(prop)) {\n            // mark it as a piece of state to be serialized\n            if ((process.env.NODE_ENV !== 'production') && hot) {\n                hotState.value[key] = toRef(setupStore, key);\n                // createOptionStore directly sets the state in pinia.state.value so we\n                // can just skip that\n            }\n            else if (!isOptionsStore) {\n                // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\n                if (initialState && shouldHydrate(prop)) {\n                    if (isRef(prop)) {\n                        prop.value = initialState[key];\n                    }\n                    else {\n                        // probably a reactive object, lets recursively assign\n                        // @ts-expect-error: prop is unknown\n                        mergeReactiveObjects(prop, initialState[key]);\n                    }\n                }\n                // transfer the ref to the pinia state to keep everything in sync\n                pinia.state.value[$id][key] = prop;\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                _hmrPayload.state.push(key);\n            }\n            // action\n        }\n        else if (typeof prop === 'function') {\n            const actionValue = (process.env.NODE_ENV !== 'production') && hot ? prop : action(prop, key);\n            // this a hot module replacement store because the hotUpdate method needs\n            // to do it with the right context\n            // @ts-expect-error\n            setupStore[key] = actionValue;\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                _hmrPayload.actions[key] = prop;\n            }\n            // list actions so they can be used in plugins\n            // @ts-expect-error\n            optionsForPlugin.actions[key] = prop;\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            // add getters for devtools\n            if (isComputed(prop)) {\n                _hmrPayload.getters[key] = isOptionsStore\n                    ? // @ts-expect-error\n                        options.getters[key]\n                    : prop;\n                if (IS_CLIENT) {\n                    const getters = setupStore._getters ||\n                        // @ts-expect-error: same\n                        (setupStore._getters = markRaw([]));\n                    getters.push(key);\n                }\n            }\n        }\n    }\n    // add the state, getters, and action properties\n    /* istanbul ignore if */\n    assign(store, setupStore);\n    // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\n    // Make `storeToRefs()` work with `reactive()` #799\n    assign(toRaw(store), setupStore);\n    // use this instead of a computed with setter to be able to create it anywhere\n    // without linking the computed lifespan to wherever the store is first\n    // created.\n    Object.defineProperty(store, '$state', {\n        get: () => ((process.env.NODE_ENV !== 'production') && hot ? hotState.value : pinia.state.value[$id]),\n        set: (state) => {\n            /* istanbul ignore if */\n            if ((process.env.NODE_ENV !== 'production') && hot) {\n                throw new Error('cannot set hotState');\n            }\n            $patch(($state) => {\n                // @ts-expect-error: FIXME: shouldn't error?\n                assign($state, state);\n            });\n        },\n    });\n    // add the hotUpdate before plugins to allow them to override it\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production')) {\n        store._hotUpdate = markRaw((newStore) => {\n            store._hotUpdating = true;\n            newStore._hmrPayload.state.forEach((stateKey) => {\n                if (stateKey in store.$state) {\n                    const newStateTarget = newStore.$state[stateKey];\n                    const oldStateSource = store.$state[stateKey];\n                    if (typeof newStateTarget === 'object' &&\n                        isPlainObject(newStateTarget) &&\n                        isPlainObject(oldStateSource)) {\n                        patchObject(newStateTarget, oldStateSource);\n                    }\n                    else {\n                        // transfer the ref\n                        newStore.$state[stateKey] = oldStateSource;\n                    }\n                }\n                // patch direct access properties to allow store.stateProperty to work as\n                // store.$state.stateProperty\n                // @ts-expect-error: any type\n                store[stateKey] = toRef(newStore.$state, stateKey);\n            });\n            // remove deleted state properties\n            Object.keys(store.$state).forEach((stateKey) => {\n                if (!(stateKey in newStore.$state)) {\n                    // @ts-expect-error: noop if doesn't exist\n                    delete store[stateKey];\n                }\n            });\n            // avoid devtools logging this as a mutation\n            isListening = false;\n            isSyncListening = false;\n            pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\n            isSyncListening = true;\n            nextTick().then(() => {\n                isListening = true;\n            });\n            for (const actionName in newStore._hmrPayload.actions) {\n                const actionFn = newStore[actionName];\n                // @ts-expect-error: actionName is a string\n                store[actionName] =\n                    //\n                    action(actionFn, actionName);\n            }\n            // TODO: does this work in both setup and option store?\n            for (const getterName in newStore._hmrPayload.getters) {\n                const getter = newStore._hmrPayload.getters[getterName];\n                const getterValue = isOptionsStore\n                    ? // special handling of options api\n                        computed(() => {\n                            setActivePinia(pinia);\n                            return getter.call(store, store);\n                        })\n                    : getter;\n                // @ts-expect-error: getterName is a string\n                store[getterName] =\n                    //\n                    getterValue;\n            }\n            // remove deleted getters\n            Object.keys(store._hmrPayload.getters).forEach((key) => {\n                if (!(key in newStore._hmrPayload.getters)) {\n                    // @ts-expect-error: noop if doesn't exist\n                    delete store[key];\n                }\n            });\n            // remove old actions\n            Object.keys(store._hmrPayload.actions).forEach((key) => {\n                if (!(key in newStore._hmrPayload.actions)) {\n                    // @ts-expect-error: noop if doesn't exist\n                    delete store[key];\n                }\n            });\n            // update the values used in devtools and to allow deleting new properties later on\n            store._hmrPayload = newStore._hmrPayload;\n            store._getters = newStore._getters;\n            store._hotUpdating = false;\n        });\n    }\n    if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n        const nonEnumerable = {\n            writable: true,\n            configurable: true,\n            // avoid warning on devtools trying to display this property\n            enumerable: false,\n        };\n        ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach((p) => {\n            Object.defineProperty(store, p, assign({ value: store[p] }, nonEnumerable));\n        });\n    }\n    // apply all plugins\n    pinia._p.forEach((extender) => {\n        /* istanbul ignore else */\n        if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n            const extensions = scope.run(() => extender({\n                store: store,\n                app: pinia._a,\n                pinia,\n                options: optionsForPlugin,\n            }));\n            Object.keys(extensions || {}).forEach((key) => store._customProperties.add(key));\n            assign(store, extensions);\n        }\n        else {\n            assign(store, scope.run(() => extender({\n                store: store,\n                app: pinia._a,\n                pinia,\n                options: optionsForPlugin,\n            })));\n        }\n    });\n    if ((process.env.NODE_ENV !== 'production') &&\n        store.$state &&\n        typeof store.$state === 'object' &&\n        typeof store.$state.constructor === 'function' &&\n        !store.$state.constructor.toString().includes('[native code]')) {\n        console.warn(`[🍍]: The \"state\" must be a plain object. It cannot be\\n` +\n            `\\tstate: () => new MyClass()\\n` +\n            `Found in store \"${store.$id}\".`);\n    }\n    // only apply hydrate to option stores with an initial state in pinia\n    if (initialState &&\n        isOptionsStore &&\n        options.hydrate) {\n        options.hydrate(store.$state, initialState);\n    }\n    isListening = true;\n    isSyncListening = true;\n    return store;\n}\n// allows unused stores to be tree shaken\n/*! #__NO_SIDE_EFFECTS__ */\nfunction defineStore(\n// TODO: add proper types from above\nid, setup, setupOptions) {\n    let options;\n    const isSetupStore = typeof setup === 'function';\n    // the option store setup will contain the actual options in this case\n    options = isSetupStore ? setupOptions : setup;\n    function useStore(pinia, hot) {\n        const hasContext = hasInjectionContext();\n        pinia =\n            // in test mode, ignore the argument provided as we can always retrieve a\n            // pinia instance with getActivePinia()\n            ((process.env.NODE_ENV === 'test') && activePinia && activePinia._testing ? null : pinia) ||\n                (hasContext ? inject(piniaSymbol, null) : null);\n        if (pinia)\n            setActivePinia(pinia);\n        if ((process.env.NODE_ENV !== 'production') && !activePinia) {\n            throw new Error(`[🍍]: \"getActivePinia()\" was called but there was no active Pinia. Are you trying to use a store before calling \"app.use(pinia)\"?\\n` +\n                `See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\\n` +\n                `This will fail in production.`);\n        }\n        pinia = activePinia;\n        if (!pinia._s.has(id)) {\n            // creating the store registers it in `pinia._s`\n            if (isSetupStore) {\n                createSetupStore(id, setup, options, pinia);\n            }\n            else {\n                createOptionsStore(id, options, pinia);\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                // @ts-expect-error: not the right inferred type\n                useStore._pinia = pinia;\n            }\n        }\n        const store = pinia._s.get(id);\n        if ((process.env.NODE_ENV !== 'production') && hot) {\n            const hotId = '__hot:' + id;\n            const newStore = isSetupStore\n                ? createSetupStore(hotId, setup, options, pinia, true)\n                : createOptionsStore(hotId, assign({}, options), pinia, true);\n            hot._hotUpdate(newStore);\n            // cleanup the state properties and the store from the cache\n            delete pinia.state.value[hotId];\n            pinia._s.delete(hotId);\n        }\n        if ((process.env.NODE_ENV !== 'production') && IS_CLIENT) {\n            const currentInstance = getCurrentInstance();\n            // save stores in instances to access them devtools\n            if (currentInstance &&\n                currentInstance.proxy &&\n                // avoid adding stores that are just built for hot module replacement\n                !hot) {\n                const vm = currentInstance.proxy;\n                const cache = '_pStores' in vm ? vm._pStores : (vm._pStores = {});\n                cache[id] = store;\n            }\n        }\n        // StoreGeneric cannot be casted towards Store\n        return store;\n    }\n    useStore.$id = id;\n    return useStore;\n}\n\nlet mapStoreSuffix = 'Store';\n/**\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\n * interface if you are using TypeScript.\n *\n * @param suffix - new suffix\n */\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\n) {\n    mapStoreSuffix = suffix;\n}\n/**\n * Allows using stores without the composition API (`setup()`) by generating an\n * object to be spread in the `computed` field of a component. It accepts a list\n * of store definitions.\n *\n * @example\n * ```js\n * export default {\n *   computed: {\n *     // other computed properties\n *     ...mapStores(useUserStore, useCartStore)\n *   },\n *\n *   created() {\n *     this.userStore // store with id \"user\"\n *     this.cartStore // store with id \"cart\"\n *   }\n * }\n * ```\n *\n * @param stores - list of stores to map to an object\n */\nfunction mapStores(...stores) {\n    if ((process.env.NODE_ENV !== 'production') && Array.isArray(stores[0])) {\n        console.warn(`[🍍]: Directly pass all stores to \"mapStores()\" without putting them in an array:\\n` +\n            `Replace\\n` +\n            `\\tmapStores([useAuthStore, useCartStore])\\n` +\n            `with\\n` +\n            `\\tmapStores(useAuthStore, useCartStore)\\n` +\n            `This will fail in production if not fixed.`);\n        stores = stores[0];\n    }\n    return stores.reduce((reduced, useStore) => {\n        // @ts-expect-error: $id is added by defineStore\n        reduced[useStore.$id + mapStoreSuffix] = function () {\n            return useStore(this.$pinia);\n        };\n        return reduced;\n    }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapState(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            reduced[key] = function () {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[key];\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function () {\n                const store = useStore(this.$pinia);\n                const storeKey = keysOrMapper[key];\n                // for some reason TS is unable to infer the type of storeKey to be a\n                // function\n                return typeof storeKey === 'function'\n                    ? storeKey.call(this, store)\n                    : // @ts-expect-error: FIXME: should work?\n                        store[storeKey];\n            };\n            return reduced;\n        }, {});\n}\n/**\n * Alias for `mapState()`. You should use `mapState()` instead.\n * @deprecated use `mapState()` instead.\n */\nconst mapGetters = mapState;\n/**\n * Allows directly using actions from your store without using the composition\n * API (`setup()`) by generating an object to be spread in the `methods` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapActions(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function (...args) {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[key](...args);\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function (...args) {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[keysOrMapper[key]](...args);\n            };\n            return reduced;\n        }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapWritableState(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            reduced[key] = {\n                get() {\n                    return useStore(this.$pinia)[key];\n                },\n                set(value) {\n                    return (useStore(this.$pinia)[key] = value);\n                },\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            reduced[key] = {\n                get() {\n                    return useStore(this.$pinia)[keysOrMapper[key]];\n                },\n                set(value) {\n                    return (useStore(this.$pinia)[keysOrMapper[key]] = value);\n                },\n            };\n            return reduced;\n        }, {});\n}\n\n/**\n * Creates an object of references with all the state, getters, and plugin-added\n * state properties of the store. Similar to `toRefs()` but specifically\n * designed for Pinia stores so methods and non reactive properties are\n * completely ignored.\n *\n * @param store - store to extract the refs from\n */\nfunction storeToRefs(store) {\n    const rawStore = toRaw(store);\n    const refs = {};\n    for (const key in rawStore) {\n        const value = rawStore[key];\n        // There is no native method to check for a computed\n        // https://github.com/vuejs/core/pull/4165\n        if (value.effect) {\n            // @ts-expect-error: too hard to type correctly\n            refs[key] =\n                // ...\n                computed({\n                    get: () => store[key],\n                    set(value) {\n                        store[key] = value;\n                    },\n                });\n        }\n        else if (isRef(value) || isReactive(value)) {\n            // @ts-expect-error: the key is state or getter\n            refs[key] =\n                // ---\n                toRef(store, key);\n        }\n    }\n    return refs;\n}\n\nexport { MutationType, acceptHMRUpdate, createPinia, defineStore, disposePinia, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, shouldHydrate, skipHydrate, storeToRefs };\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,WAAW,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,eAAe,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,KAAK;AACtN,SAASC,mBAAmB,QAAQ,mBAAmB;;AAEvD;AACA;AACA;AACA;AACA,IAAIC,WAAW;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIC,KAAK,IAAMF,WAAW,GAAGE,KAAM;AACvD;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAOtB,mBAAmB,CAAC,CAAC,IAAIC,MAAM,CAACsB,WAAW,CAAC,IAAKJ,WAAW;AAC1F,MAAMI,WAAW,GAAKC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAIC,MAAM,CAAC,OAAO,CAAC,GAAG,0BAA2BA,MAAM,CAAC,CAAE;AAErH,SAASC,aAAaA;AACtB;AACAC,CAAC,EAAE;EACC,OAAQA,CAAC,IACL,OAAOA,CAAC,KAAK,QAAQ,IACrBC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,KAAK,iBAAiB,IACvD,OAAOA,CAAC,CAACK,MAAM,KAAK,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjC;AACJ;AACA;AACA;AACA;EACIA,YAAY,CAAC,aAAa,CAAC,GAAG,cAAc;EAC5C;AACJ;AACA;AACA;AACA;EACIA,YAAY,CAAC,eAAe,CAAC,GAAG,gBAAgB;EAChD;AACJ,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AAEvC,MAAMC,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,aAAc,CAAC,MAAM,OAAOD,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACA,MAAM,KAAKA,MAAM,GACrFA,MAAM,GACN,OAAOE,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACA,IAAI,KAAKA,IAAI,GAC1CA,IAAI,GACJ,OAAOC,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACA,MAAM,KAAKA,MAAM,GAClDA,MAAM,GACN,OAAOC,UAAU,KAAK,QAAQ,GAC1BA,UAAU,GACV;EAAEC,WAAW,EAAE;AAAK,CAAC,EAAE,CAAC;AAC1C,SAASC,GAAGA,CAACC,IAAI,EAAE;EAAEC,OAAO,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EACzC;EACA;EACA,IAAIA,OAAO,IACP,4EAA4E,CAACC,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;IAC9F,OAAO,IAAIC,IAAI,CAAC,CAACC,MAAM,CAACC,YAAY,CAAC,MAAM,CAAC,EAAEN,IAAI,CAAC,EAAE;MAAEG,IAAI,EAAEH,IAAI,CAACG;IAAK,CAAC,CAAC;EAC7E;EACA,OAAOH,IAAI;AACf;AACA,SAASO,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC/B,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAEL,GAAG,CAAC;EACpBG,GAAG,CAACG,YAAY,GAAG,MAAM;EACzBH,GAAG,CAACI,MAAM,GAAG,YAAY;IACrBC,MAAM,CAACL,GAAG,CAACM,QAAQ,EAAER,IAAI,EAAEC,IAAI,CAAC;EACpC,CAAC;EACDC,GAAG,CAACO,OAAO,GAAG,YAAY;IACtBC,OAAO,CAACC,KAAK,CAAC,yBAAyB,CAAC;EAC5C,CAAC;EACDT,GAAG,CAACU,IAAI,CAAC,CAAC;AACd;AACA,SAASC,WAAWA,CAACd,GAAG,EAAE;EACtB,MAAMG,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;EAChC;EACAD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAEL,GAAG,EAAE,KAAK,CAAC;EAC5B,IAAI;IACAG,GAAG,CAACU,IAAI,CAAC,CAAC;EACd,CAAC,CACD,OAAOE,CAAC,EAAE,CAAE;EACZ,OAAOZ,GAAG,CAACa,MAAM,IAAI,GAAG,IAAIb,GAAG,CAACa,MAAM,IAAI,GAAG;AACjD;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACjB,IAAI;IACAA,IAAI,CAACC,aAAa,CAAC,IAAIC,UAAU,CAAC,OAAO,CAAC,CAAC;EAC/C,CAAC,CACD,OAAOL,CAAC,EAAE;IACN,MAAMM,GAAG,GAAG,IAAID,UAAU,CAAC,OAAO,EAAE;MAChCE,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,IAAI;MAChBC,IAAI,EAAEvC,MAAM;MACZwC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE;IACnB,CAAC,CAAC;IACFjB,IAAI,CAACC,aAAa,CAACE,GAAG,CAAC;EAC3B;AACJ;AACA,MAAMe,UAAU,GAAG,OAAOC,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG;EAAEC,SAAS,EAAE;AAAG,CAAC;AAChF;AACA;AACA;AACA,MAAMC,cAAc,GAAG,aAAc,CAAC,MAAM,WAAW,CAAC7C,IAAI,CAAC0C,UAAU,CAACE,SAAS,CAAC,IAC9E,aAAa,CAAC5C,IAAI,CAAC0C,UAAU,CAACE,SAAS,CAAC,IACxC,CAAC,QAAQ,CAAC5C,IAAI,CAAC0C,UAAU,CAACE,SAAS,CAAC,EAAE,CAAC;AAC3C,MAAM9B,MAAM,GAAG,CAACxB,SAAS,GACnB,MAAM,CAAE,CAAC,CAAC;AAAA;AACV;AACE,OAAOwD,iBAAiB,KAAK,WAAW,IACpC,UAAU,IAAIA,iBAAiB,CAAC7D,SAAS,IACzC,CAAC4D,cAAc,GACbE,cAAc;AACd;AACE,kBAAkB,IAAIL,UAAU,GAC1BM,QAAQ;AACR;AACEC,eAAe;AACvC,SAASF,cAAcA,CAACjD,IAAI,EAAES,IAAI,GAAG,UAAU,EAAEC,IAAI,EAAE;EACnD,MAAM0C,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACrCF,CAAC,CAAC7C,QAAQ,GAAGE,IAAI;EACjB2C,CAAC,CAACG,GAAG,GAAG,UAAU,CAAC,CAAC;EACpB;EACA;EACA,IAAI,OAAOvD,IAAI,KAAK,QAAQ,EAAE;IAC1B;IACAoD,CAAC,CAACI,IAAI,GAAGxD,IAAI;IACb,IAAIoD,CAAC,CAACK,MAAM,KAAKC,QAAQ,CAACD,MAAM,EAAE;MAC9B,IAAInC,WAAW,CAAC8B,CAAC,CAACI,IAAI,CAAC,EAAE;QACrBjD,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAEC,IAAI,CAAC;MAC9B,CAAC,MACI;QACD0C,CAAC,CAACO,MAAM,GAAG,QAAQ;QACnBlC,KAAK,CAAC2B,CAAC,CAAC;MACZ;IACJ,CAAC,MACI;MACD3B,KAAK,CAAC2B,CAAC,CAAC;IACZ;EACJ,CAAC,MACI;IACD;IACAA,CAAC,CAACI,IAAI,GAAGI,GAAG,CAACC,eAAe,CAAC7D,IAAI,CAAC;IAClC8D,UAAU,CAAC,YAAY;MACnBF,GAAG,CAACG,eAAe,CAACX,CAAC,CAACI,IAAI,CAAC;IAC/B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACTM,UAAU,CAAC,YAAY;MACnBrC,KAAK,CAAC2B,CAAC,CAAC;IACZ,CAAC,EAAE,CAAC,CAAC;EACT;AACJ;AACA,SAASF,QAAQA,CAAClD,IAAI,EAAES,IAAI,GAAG,UAAU,EAAEC,IAAI,EAAE;EAC7C,IAAI,OAAOV,IAAI,KAAK,QAAQ,EAAE;IAC1B,IAAIsB,WAAW,CAACtB,IAAI,CAAC,EAAE;MACnBO,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAEC,IAAI,CAAC;IAC9B,CAAC,MACI;MACD,MAAM0C,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACI,IAAI,GAAGxD,IAAI;MACboD,CAAC,CAACO,MAAM,GAAG,QAAQ;MACnBG,UAAU,CAAC,YAAY;QACnBrC,KAAK,CAAC2B,CAAC,CAAC;MACZ,CAAC,CAAC;IACN;EACJ,CAAC,MACI;IACD;IACAP,SAAS,CAACmB,gBAAgB,CAACjE,GAAG,CAACC,IAAI,EAAEU,IAAI,CAAC,EAAED,IAAI,CAAC;EACrD;AACJ;AACA,SAAS0C,eAAeA,CAACnD,IAAI,EAAES,IAAI,EAAEC,IAAI,EAAEuD,KAAK,EAAE;EAC9C;EACA;EACAA,KAAK,GAAGA,KAAK,IAAIpD,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;EACnC,IAAIoD,KAAK,EAAE;IACPA,KAAK,CAACZ,QAAQ,CAACa,KAAK,GAAGD,KAAK,CAACZ,QAAQ,CAACc,IAAI,CAACC,SAAS,GAAG,gBAAgB;EAC3E;EACA,IAAI,OAAOpE,IAAI,KAAK,QAAQ,EACxB,OAAOO,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAEC,IAAI,CAAC;EACrC,MAAM2D,KAAK,GAAGrE,IAAI,CAACG,IAAI,KAAK,0BAA0B;EACtD,MAAMmE,QAAQ,GAAG,cAAc,CAACpE,IAAI,CAACG,MAAM,CAACX,OAAO,CAACI,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAIJ,OAAO;EACxF,MAAM6E,WAAW,GAAG,cAAc,CAACrE,IAAI,CAAC2C,SAAS,CAACC,SAAS,CAAC;EAC5D,IAAI,CAACyB,WAAW,IAAKF,KAAK,IAAIC,QAAS,IAAIvB,cAAc,KACrD,OAAOyB,UAAU,KAAK,WAAW,EAAE;IACnC;IACA,MAAMC,MAAM,GAAG,IAAID,UAAU,CAAC,CAAC;IAC/BC,MAAM,CAACC,SAAS,GAAG,YAAY;MAC3B,IAAIlE,GAAG,GAAGiE,MAAM,CAACE,MAAM;MACvB,IAAI,OAAOnE,GAAG,KAAK,QAAQ,EAAE;QACzByD,KAAK,GAAG,IAAI;QACZ,MAAM,IAAIW,KAAK,CAAC,0BAA0B,CAAC;MAC/C;MACApE,GAAG,GAAG+D,WAAW,GACX/D,GAAG,GACHA,GAAG,CAACqE,OAAO,CAAC,cAAc,EAAE,uBAAuB,CAAC;MAC1D,IAAIZ,KAAK,EAAE;QACPA,KAAK,CAACP,QAAQ,CAACF,IAAI,GAAGhD,GAAG;MAC7B,CAAC,MACI;QACDkD,QAAQ,CAACoB,MAAM,CAACtE,GAAG,CAAC;MACxB;MACAyD,KAAK,GAAG,IAAI,CAAC,CAAC;IAClB,CAAC;IACDQ,MAAM,CAACM,aAAa,CAAC/E,IAAI,CAAC;EAC9B,CAAC,MACI;IACD,MAAMQ,GAAG,GAAGoD,GAAG,CAACC,eAAe,CAAC7D,IAAI,CAAC;IACrC,IAAIiE,KAAK,EACLA,KAAK,CAACP,QAAQ,CAACoB,MAAM,CAACtE,GAAG,CAAC,CAAC,KAE3BkD,QAAQ,CAACF,IAAI,GAAGhD,GAAG;IACvByD,KAAK,GAAG,IAAI,CAAC,CAAC;IACdH,UAAU,CAAC,YAAY;MACnBF,GAAG,CAACG,eAAe,CAACvD,GAAG,CAAC;IAC5B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACb;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwE,YAAYA,CAACC,OAAO,EAAE9E,IAAI,EAAE;EACjC,MAAM+E,YAAY,GAAG,KAAK,GAAGD,OAAO;EACpC,IAAI,OAAOE,sBAAsB,KAAK,UAAU,EAAE;IAC9C;IACAA,sBAAsB,CAACD,YAAY,EAAE/E,IAAI,CAAC;EAC9C,CAAC,MACI,IAAIA,IAAI,KAAK,OAAO,EAAE;IACvBgB,OAAO,CAACC,KAAK,CAAC8D,YAAY,CAAC;EAC/B,CAAC,MACI,IAAI/E,IAAI,KAAK,MAAM,EAAE;IACtBgB,OAAO,CAACiE,IAAI,CAACF,YAAY,CAAC;EAC9B,CAAC,MACI;IACD/D,OAAO,CAACkE,GAAG,CAACH,YAAY,CAAC;EAC7B;AACJ;AACA,SAASI,OAAOA,CAACrG,CAAC,EAAE;EAChB,OAAO,IAAI,IAAIA,CAAC,IAAI,SAAS,IAAIA,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA,SAASsG,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,EAAE,WAAW,IAAI1C,SAAS,CAAC,EAAE;IAC7BmC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;IACvE,OAAO,IAAI;EACf;AACJ;AACA,SAASQ,oBAAoBA,CAACpE,KAAK,EAAE;EACjC,IAAIA,KAAK,YAAYwD,KAAK,IACtBxD,KAAK,CAAC6D,OAAO,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,yBAAyB,CAAC,EAAE;IACjEV,YAAY,CAAC,iGAAiG,EAAE,MAAM,CAAC;IACvH,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,eAAeW,qBAAqBA,CAAClH,KAAK,EAAE;EACxC,IAAI8G,oBAAoB,CAAC,CAAC,EACtB;EACJ,IAAI;IACA,MAAM1C,SAAS,CAAC+C,SAAS,CAACC,SAAS,CAACC,IAAI,CAACC,SAAS,CAACtH,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC,CAAC;IACtEjB,YAAY,CAAC,mCAAmC,CAAC;EACrD,CAAC,CACD,OAAO5D,KAAK,EAAE;IACV,IAAIoE,oBAAoB,CAACpE,KAAK,CAAC,EAC3B;IACJ4D,YAAY,CAAC,oEAAoE,EAAE,OAAO,CAAC;IAC3F7D,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,eAAe8E,sBAAsBA,CAACzH,KAAK,EAAE;EACzC,IAAI8G,oBAAoB,CAAC,CAAC,EACtB;EACJ,IAAI;IACAY,eAAe,CAAC1H,KAAK,EAAEqH,IAAI,CAACM,KAAK,CAAC,MAAMvD,SAAS,CAAC+C,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxErB,YAAY,CAAC,qCAAqC,CAAC;EACvD,CAAC,CACD,OAAO5D,KAAK,EAAE;IACV,IAAIoE,oBAAoB,CAACpE,KAAK,CAAC,EAC3B;IACJ4D,YAAY,CAAC,qFAAqF,EAAE,OAAO,CAAC;IAC5G7D,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,eAAekF,qBAAqBA,CAAC7H,KAAK,EAAE;EACxC,IAAI;IACAuC,MAAM,CAAC,IAAIZ,IAAI,CAAC,CAAC0F,IAAI,CAACC,SAAS,CAACtH,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE;MACjD9F,IAAI,EAAE;IACV,CAAC,CAAC,EAAE,kBAAkB,CAAC;EAC3B,CAAC,CACD,OAAOiB,KAAK,EAAE;IACV4D,YAAY,CAAC,yEAAyE,EAAE,OAAO,CAAC;IAChG7D,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,IAAImF,SAAS;AACb,SAASC,aAAaA,CAAA,EAAG;EACrB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAGlD,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC3CiD,SAAS,CAACpG,IAAI,GAAG,MAAM;IACvBoG,SAAS,CAACE,MAAM,GAAG,OAAO;EAC9B;EACA,SAASC,QAAQA,CAAA,EAAG;IAChB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpCN,SAAS,CAACO,QAAQ,GAAG,YAAY;QAC7B,MAAMC,KAAK,GAAGR,SAAS,CAACQ,KAAK;QAC7B,IAAI,CAACA,KAAK,EACN,OAAOH,OAAO,CAAC,IAAI,CAAC;QACxB,MAAMI,IAAI,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC;QAC1B,IAAI,CAACD,IAAI,EACL,OAAOJ,OAAO,CAAC,IAAI,CAAC;QACxB,OAAOA,OAAO,CAAC;UAAEM,IAAI,EAAE,MAAMF,IAAI,CAACE,IAAI,CAAC,CAAC;UAAEF;QAAK,CAAC,CAAC;MACrD,CAAC;MACD;MACAT,SAAS,CAACY,QAAQ,GAAG,MAAMP,OAAO,CAAC,IAAI,CAAC;MACxCL,SAAS,CAACrF,OAAO,GAAG2F,MAAM;MAC1BN,SAAS,CAAC9E,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC;EACN;EACA,OAAOiF,QAAQ;AACnB;AACA,eAAeU,yBAAyBA,CAAC3I,KAAK,EAAE;EAC5C,IAAI;IACA,MAAMoC,IAAI,GAAG2F,aAAa,CAAC,CAAC;IAC5B,MAAM7B,MAAM,GAAG,MAAM9D,IAAI,CAAC,CAAC;IAC3B,IAAI,CAAC8D,MAAM,EACP;IACJ,MAAM;MAAEuC,IAAI;MAAEF;IAAK,CAAC,GAAGrC,MAAM;IAC7BwB,eAAe,CAAC1H,KAAK,EAAEqH,IAAI,CAACM,KAAK,CAACc,IAAI,CAAC,CAAC;IACxClC,YAAY,CAAC,+BAA+BgC,IAAI,CAACvG,IAAI,IAAI,CAAC;EAC9D,CAAC,CACD,OAAOW,KAAK,EAAE;IACV4D,YAAY,CAAC,2EAA2E,EAAE,OAAO,CAAC;IAClG7D,OAAO,CAACC,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ;AACA,SAAS+E,eAAeA,CAAC1H,KAAK,EAAEuH,KAAK,EAAE;EACnC,KAAK,MAAMqB,GAAG,IAAIrB,KAAK,EAAE;IACrB,MAAMsB,UAAU,GAAG7I,KAAK,CAACuH,KAAK,CAACC,KAAK,CAACoB,GAAG,CAAC;IACzC;IACA,IAAIC,UAAU,EAAE;MACZpI,MAAM,CAAC4F,MAAM,CAACwC,UAAU,EAAEtB,KAAK,CAACqB,GAAG,CAAC,CAAC;IACzC,CAAC,MACI;MACD;MACA5I,KAAK,CAACuH,KAAK,CAACC,KAAK,CAACoB,GAAG,CAAC,GAAGrB,KAAK,CAACqB,GAAG,CAAC;IACvC;EACJ;AACJ;AAEA,SAASE,aAAaA,CAACC,OAAO,EAAE;EAC5B,OAAO;IACHC,OAAO,EAAE;MACLD;IACJ;EACJ,CAAC;AACL;AACA,MAAME,gBAAgB,GAAG,iBAAiB;AAC1C,MAAMC,aAAa,GAAG,OAAO;AAC7B,SAASC,2BAA2BA,CAACC,KAAK,EAAE;EACxC,OAAOvC,OAAO,CAACuC,KAAK,CAAC,GACf;IACEC,EAAE,EAAEH,aAAa;IACjBI,KAAK,EAAEL;EACX,CAAC,GACC;IACEI,EAAE,EAAED,KAAK,CAACG,GAAG;IACbD,KAAK,EAAEF,KAAK,CAACG;EACjB,CAAC;AACT;AACA,SAASC,4BAA4BA,CAACJ,KAAK,EAAE;EACzC,IAAIvC,OAAO,CAACuC,KAAK,CAAC,EAAE;IAChB,MAAMK,UAAU,GAAGC,KAAK,CAACC,IAAI,CAACP,KAAK,CAACQ,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9C,MAAMC,QAAQ,GAAGV,KAAK,CAACQ,EAAE;IACzB,MAAMrC,KAAK,GAAG;MACVA,KAAK,EAAEkC,UAAU,CAACM,GAAG,CAAEC,OAAO,KAAM;QAChCC,QAAQ,EAAE,IAAI;QACdrB,GAAG,EAAEoB,OAAO;QACZxC,KAAK,EAAE4B,KAAK,CAAC7B,KAAK,CAACC,KAAK,CAACwC,OAAO;MACpC,CAAC,CAAC,CAAC;MACHE,OAAO,EAAET,UAAU,CACdU,MAAM,CAAEd,EAAE,IAAKS,QAAQ,CAACM,GAAG,CAACf,EAAE,CAAC,CAACgB,QAAQ,CAAC,CACzCN,GAAG,CAAEV,EAAE,IAAK;QACb,MAAMD,KAAK,GAAGU,QAAQ,CAACM,GAAG,CAACf,EAAE,CAAC;QAC9B,OAAO;UACHY,QAAQ,EAAE,KAAK;UACfrB,GAAG,EAAES,EAAE;UACP7B,KAAK,EAAE4B,KAAK,CAACiB,QAAQ,CAACC,MAAM,CAAC,CAACJ,OAAO,EAAEtB,GAAG,KAAK;YAC3CsB,OAAO,CAACtB,GAAG,CAAC,GAAGQ,KAAK,CAACR,GAAG,CAAC;YACzB,OAAOsB,OAAO;UAClB,CAAC,EAAE,CAAC,CAAC;QACT,CAAC;MACL,CAAC;IACL,CAAC;IACD,OAAO3C,KAAK;EAChB;EACA,MAAMA,KAAK,GAAG;IACVA,KAAK,EAAE9G,MAAM,CAACoJ,IAAI,CAACT,KAAK,CAACmB,MAAM,CAAC,CAACR,GAAG,CAAEnB,GAAG,KAAM;MAC3CqB,QAAQ,EAAE,IAAI;MACdrB,GAAG;MACHpB,KAAK,EAAE4B,KAAK,CAACmB,MAAM,CAAC3B,GAAG;IAC3B,CAAC,CAAC;EACN,CAAC;EACD;EACA,IAAIQ,KAAK,CAACiB,QAAQ,IAAIjB,KAAK,CAACiB,QAAQ,CAACG,MAAM,EAAE;IACzCjD,KAAK,CAAC2C,OAAO,GAAGd,KAAK,CAACiB,QAAQ,CAACN,GAAG,CAAEU,UAAU,KAAM;MAChDR,QAAQ,EAAE,KAAK;MACfrB,GAAG,EAAE6B,UAAU;MACfjD,KAAK,EAAE4B,KAAK,CAACqB,UAAU;IAC3B,CAAC,CAAC,CAAC;EACP;EACA,IAAIrB,KAAK,CAACsB,iBAAiB,CAACC,IAAI,EAAE;IAC9BpD,KAAK,CAACqD,gBAAgB,GAAGlB,KAAK,CAACC,IAAI,CAACP,KAAK,CAACsB,iBAAiB,CAAC,CAACX,GAAG,CAAEnB,GAAG,KAAM;MACvEqB,QAAQ,EAAE,IAAI;MACdrB,GAAG;MACHpB,KAAK,EAAE4B,KAAK,CAACR,GAAG;IACpB,CAAC,CAAC,CAAC;EACP;EACA,OAAOrB,KAAK;AAChB;AACA,SAASsD,eAAeA,CAACC,MAAM,EAAE;EAC7B,IAAI,CAACA,MAAM,EACP,OAAO,CAAC,CAAC;EACb,IAAIpB,KAAK,CAACqB,OAAO,CAACD,MAAM,CAAC,EAAE;IACvB;IACA,OAAOA,MAAM,CAACR,MAAM,CAAC,CAACU,IAAI,EAAEC,KAAK,KAAK;MAClCD,IAAI,CAACnB,IAAI,CAACqB,IAAI,CAACD,KAAK,CAACrC,GAAG,CAAC;MACzBoC,IAAI,CAACG,UAAU,CAACD,IAAI,CAACD,KAAK,CAACvJ,IAAI,CAAC;MAChCsJ,IAAI,CAACI,QAAQ,CAACH,KAAK,CAACrC,GAAG,CAAC,GAAGqC,KAAK,CAACG,QAAQ;MACzCJ,IAAI,CAACK,QAAQ,CAACJ,KAAK,CAACrC,GAAG,CAAC,GAAGqC,KAAK,CAACI,QAAQ;MACzC,OAAOL,IAAI;IACf,CAAC,EAAE;MACCI,QAAQ,EAAE,CAAC,CAAC;MACZvB,IAAI,EAAE,EAAE;MACRsB,UAAU,EAAE,EAAE;MACdE,QAAQ,EAAE,CAAC;IACf,CAAC,CAAC;EACN,CAAC,MACI;IACD,OAAO;MACHC,SAAS,EAAExC,aAAa,CAACgC,MAAM,CAACpJ,IAAI,CAAC;MACrCkH,GAAG,EAAEE,aAAa,CAACgC,MAAM,CAAClC,GAAG,CAAC;MAC9BwC,QAAQ,EAAEN,MAAM,CAACM,QAAQ;MACzBC,QAAQ,EAAEP,MAAM,CAACO;IACrB,CAAC;EACL;AACJ;AACA,SAASE,kBAAkBA,CAAC7J,IAAI,EAAE;EAC9B,QAAQA,IAAI;IACR,KAAKZ,YAAY,CAAC0K,MAAM;MACpB,OAAO,UAAU;IACrB,KAAK1K,YAAY,CAAC2K,aAAa;MAC3B,OAAO,QAAQ;IACnB,KAAK3K,YAAY,CAAC4K,WAAW;MACzB,OAAO,QAAQ;IACnB;MACI,OAAO,SAAS;EACxB;AACJ;;AAEA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,kBAAkB,GAAG,iBAAiB;AAC5C,MAAMC,YAAY,GAAG,OAAO;AAC5B,MAAM;EAAEzF,MAAM,EAAE0F;AAAS,CAAC,GAAGtL,MAAM;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuL,YAAY,GAAI3C,EAAE,IAAK,KAAK,GAAGA,EAAE;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4C,qBAAqBA,CAACC,GAAG,EAAElM,KAAK,EAAE;EACvCH,mBAAmB,CAAC;IAChBwJ,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,UAAU;IACjB6C,IAAI,EAAE,kCAAkC;IACxCC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,yBAAyB;IACnCT,mBAAmB;IACnBM;EACJ,CAAC,EAAGI,GAAG,IAAK;IACR,IAAI,OAAOA,GAAG,CAACC,GAAG,KAAK,UAAU,EAAE;MAC/BhG,YAAY,CAAC,yMAAyM,CAAC;IAC3N;IACA+F,GAAG,CAACE,gBAAgB,CAAC;MACjBnD,EAAE,EAAEwC,kBAAkB;MACtBvC,KAAK,EAAE,UAAU;MACjBmD,KAAK,EAAE;IACX,CAAC,CAAC;IACFH,GAAG,CAACI,YAAY,CAAC;MACbrD,EAAE,EAAEyC,YAAY;MAChBxC,KAAK,EAAE,UAAU;MACjBqD,IAAI,EAAE,SAAS;MACfC,qBAAqB,EAAE,eAAe;MACtCC,OAAO,EAAE,CACL;QACIF,IAAI,EAAE,cAAc;QACpBG,MAAM,EAAEA,CAAA,KAAM;UACV5F,qBAAqB,CAAClH,KAAK,CAAC;QAChC,CAAC;QACD+M,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,eAAe;QACrBG,MAAM,EAAE,MAAAA,CAAA,KAAY;UAChB,MAAMrF,sBAAsB,CAACzH,KAAK,CAAC;UACnCsM,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;UACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;QACxC,CAAC;QACDiB,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,MAAM;QACZG,MAAM,EAAEA,CAAA,KAAM;UACVjF,qBAAqB,CAAC7H,KAAK,CAAC;QAChC,CAAC;QACD+M,OAAO,EAAE;MACb,CAAC,EACD;QACIJ,IAAI,EAAE,aAAa;QACnBG,MAAM,EAAE,MAAAA,CAAA,KAAY;UAChB,MAAMnE,yBAAyB,CAAC3I,KAAK,CAAC;UACtCsM,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;UACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;QACxC,CAAC;QACDiB,OAAO,EAAE;MACb,CAAC,CACJ;MACDG,WAAW,EAAE,CACT;QACIP,IAAI,EAAE,SAAS;QACfI,OAAO,EAAE,iCAAiC;QAC1CD,MAAM,EAAGK,MAAM,IAAK;UAChB,MAAM/D,KAAK,GAAGpJ,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAAC+C,MAAM,CAAC;UAClC,IAAI,CAAC/D,KAAK,EAAE;YACR7C,YAAY,CAAC,iBAAiB4G,MAAM,kCAAkC,EAAE,MAAM,CAAC;UACnF,CAAC,MACI,IAAI,OAAO/D,KAAK,CAACgE,MAAM,KAAK,UAAU,EAAE;YACzC7G,YAAY,CAAC,iBAAiB4G,MAAM,gEAAgE,EAAE,MAAM,CAAC;UACjH,CAAC,MACI;YACD/D,KAAK,CAACgE,MAAM,CAAC,CAAC;YACd7G,YAAY,CAAC,UAAU4G,MAAM,UAAU,CAAC;UAC5C;QACJ;MACJ,CAAC;IAET,CAAC,CAAC;IACFb,GAAG,CAACe,EAAE,CAACC,gBAAgB,CAAEC,OAAO,IAAK;MACjC,MAAMC,KAAK,GAAID,OAAO,CAACE,iBAAiB,IACpCF,OAAO,CAACE,iBAAiB,CAACD,KAAM;MACpC,IAAIA,KAAK,IAAIA,KAAK,CAACE,QAAQ,EAAE;QACzB,MAAMC,WAAW,GAAGJ,OAAO,CAACE,iBAAiB,CAACD,KAAK,CAACE,QAAQ;QAC5DjN,MAAM,CAACmN,MAAM,CAACD,WAAW,CAAC,CAACE,OAAO,CAAEzE,KAAK,IAAK;UAC1CmE,OAAO,CAACO,YAAY,CAACvG,KAAK,CAAC2D,IAAI,CAAC;YAC5BxJ,IAAI,EAAEsK,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC;YAC7BX,GAAG,EAAE,OAAO;YACZqB,QAAQ,EAAE,IAAI;YACdzC,KAAK,EAAE4B,KAAK,CAAC2E,aAAa,GACpB;cACE/E,OAAO,EAAE;gBACLxB,KAAK,EAAE3I,KAAK,CAACuK,KAAK,CAACmB,MAAM,CAAC;gBAC1BsC,OAAO,EAAE,CACL;kBACIF,IAAI,EAAE,SAAS;kBACfI,OAAO,EAAE,+BAA+B;kBACxCD,MAAM,EAAEA,CAAA,KAAM1D,KAAK,CAACgE,MAAM,CAAC;gBAC/B,CAAC;cAET;YACJ,CAAC;YACC;YACE3M,MAAM,CAACoJ,IAAI,CAACT,KAAK,CAACmB,MAAM,CAAC,CAACD,MAAM,CAAC,CAAC/C,KAAK,EAAEqB,GAAG,KAAK;cAC7CrB,KAAK,CAACqB,GAAG,CAAC,GAAGQ,KAAK,CAACmB,MAAM,CAAC3B,GAAG,CAAC;cAC9B,OAAOrB,KAAK;YAChB,CAAC,EAAE,CAAC,CAAC;UACjB,CAAC,CAAC;UACF,IAAI6B,KAAK,CAACiB,QAAQ,IAAIjB,KAAK,CAACiB,QAAQ,CAACG,MAAM,EAAE;YACzC+C,OAAO,CAACO,YAAY,CAACvG,KAAK,CAAC2D,IAAI,CAAC;cAC5BxJ,IAAI,EAAEsK,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC;cAC7BX,GAAG,EAAE,SAAS;cACdqB,QAAQ,EAAE,KAAK;cACfzC,KAAK,EAAE4B,KAAK,CAACiB,QAAQ,CAACC,MAAM,CAAC,CAACJ,OAAO,EAAEtB,GAAG,KAAK;gBAC3C,IAAI;kBACAsB,OAAO,CAACtB,GAAG,CAAC,GAAGQ,KAAK,CAACR,GAAG,CAAC;gBAC7B,CAAC,CACD,OAAOjG,KAAK,EAAE;kBACV;kBACAuH,OAAO,CAACtB,GAAG,CAAC,GAAGjG,KAAK;gBACxB;gBACA,OAAOuH,OAAO;cAClB,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACFoC,GAAG,CAACe,EAAE,CAACW,gBAAgB,CAAET,OAAO,IAAK;MACjC,IAAIA,OAAO,CAACrB,GAAG,KAAKA,GAAG,IAAIqB,OAAO,CAACU,WAAW,KAAKnC,YAAY,EAAE;QAC7D,IAAIoC,MAAM,GAAG,CAAClO,KAAK,CAAC;QACpBkO,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACzE,KAAK,CAACC,IAAI,CAAC3J,KAAK,CAAC4J,EAAE,CAACgE,MAAM,CAAC,CAAC,CAAC,CAAC;QACrDL,OAAO,CAACa,SAAS,GAAG,CAACb,OAAO,CAACpD,MAAM,GAC7B+D,MAAM,CAAC/D,MAAM,CAAEf,KAAK,IAAK,KAAK,IAAIA,KAAK,GACnCA,KAAK,CAACG,GAAG,CACNvC,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACsG,OAAO,CAACpD,MAAM,CAACnD,WAAW,CAAC,CAAC,CAAC,GACzCiC,gBAAgB,CAACjC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACsG,OAAO,CAACpD,MAAM,CAACnD,WAAW,CAAC,CAAC,CAAC,CAAC,GAC1EkH,MAAM,EAAEnE,GAAG,CAACZ,2BAA2B,CAAC;MAClD;IACJ,CAAC,CAAC;IACF;IACA/H,UAAU,CAACiN,MAAM,GAAGrO,KAAK;IACzBsM,GAAG,CAACe,EAAE,CAACiB,iBAAiB,CAAEf,OAAO,IAAK;MAClC,IAAIA,OAAO,CAACrB,GAAG,KAAKA,GAAG,IAAIqB,OAAO,CAACU,WAAW,KAAKnC,YAAY,EAAE;QAC7D,MAAMyC,cAAc,GAAGhB,OAAO,CAACJ,MAAM,KAAKjE,aAAa,GACjDlJ,KAAK,GACLA,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAACmD,OAAO,CAACJ,MAAM,CAAC;QAClC,IAAI,CAACoB,cAAc,EAAE;UACjB;UACA;UACA;QACJ;QACA,IAAIA,cAAc,EAAE;UAChB;UACA,IAAIhB,OAAO,CAACJ,MAAM,KAAKjE,aAAa,EAChC9H,UAAU,CAACoN,MAAM,GAAG3P,KAAK,CAAC0P,cAAc,CAAC;UAC7ChB,OAAO,CAAChG,KAAK,GAAGiC,4BAA4B,CAAC+E,cAAc,CAAC;QAChE;MACJ;IACJ,CAAC,CAAC;IACFjC,GAAG,CAACe,EAAE,CAACoB,kBAAkB,CAAElB,OAAO,IAAK;MACnC,IAAIA,OAAO,CAACrB,GAAG,KAAKA,GAAG,IAAIqB,OAAO,CAACU,WAAW,KAAKnC,YAAY,EAAE;QAC7D,MAAMyC,cAAc,GAAGhB,OAAO,CAACJ,MAAM,KAAKjE,aAAa,GACjDlJ,KAAK,GACLA,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAACmD,OAAO,CAACJ,MAAM,CAAC;QAClC,IAAI,CAACoB,cAAc,EAAE;UACjB,OAAOhI,YAAY,CAAC,UAAUgH,OAAO,CAACJ,MAAM,aAAa,EAAE,OAAO,CAAC;QACvE;QACA,MAAM;UAAEuB;QAAK,CAAC,GAAGnB,OAAO;QACxB,IAAI,CAAC1G,OAAO,CAAC0H,cAAc,CAAC,EAAE;UAC1B;UACA,IAAIG,IAAI,CAAClE,MAAM,KAAK,CAAC,IACjB,CAAC+D,cAAc,CAAC7D,iBAAiB,CAACiE,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,IAC9CA,IAAI,CAAC,CAAC,CAAC,IAAIH,cAAc,CAAChE,MAAM,EAAE;YAClCmE,IAAI,CAACE,OAAO,CAAC,QAAQ,CAAC;UAC1B;QACJ,CAAC,MACI;UACD;UACAF,IAAI,CAACE,OAAO,CAAC,OAAO,CAAC;QACzB;QACAjD,gBAAgB,GAAG,KAAK;QACxB4B,OAAO,CAACsB,GAAG,CAACN,cAAc,EAAEG,IAAI,EAAEnB,OAAO,CAAChG,KAAK,CAACC,KAAK,CAAC;QACtDmE,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;IACFW,GAAG,CAACe,EAAE,CAACyB,kBAAkB,CAAEvB,OAAO,IAAK;MACnC,IAAIA,OAAO,CAAC7L,IAAI,CAACqN,UAAU,CAAC,IAAI,CAAC,EAAE;QAC/B,MAAM/E,OAAO,GAAGuD,OAAO,CAAC7L,IAAI,CAAC0E,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QAClD,MAAMgD,KAAK,GAAGpJ,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAACJ,OAAO,CAAC;QACnC,IAAI,CAACZ,KAAK,EAAE;UACR,OAAO7C,YAAY,CAAC,UAAUyD,OAAO,aAAa,EAAE,OAAO,CAAC;QAChE;QACA,MAAM;UAAE0E;QAAK,CAAC,GAAGnB,OAAO;QACxB,IAAImB,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACrB,OAAOnI,YAAY,CAAC,2BAA2ByD,OAAO,OAAO0E,IAAI,+BAA+B,CAAC;QACrG;QACA;QACA;QACAA,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ;QAClB/C,gBAAgB,GAAG,KAAK;QACxB4B,OAAO,CAACsB,GAAG,CAACzF,KAAK,EAAEsF,IAAI,EAAEnB,OAAO,CAAChG,KAAK,CAACC,KAAK,CAAC;QAC7CmE,gBAAgB,GAAG,IAAI;MAC3B;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASqD,kBAAkBA,CAAC9C,GAAG,EAAE9C,KAAK,EAAE;EACpC,IAAI,CAACwC,mBAAmB,CAAC3E,QAAQ,CAAC+E,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC,CAAC,EAAE;IACxDqC,mBAAmB,CAACV,IAAI,CAACc,YAAY,CAAC5C,KAAK,CAACG,GAAG,CAAC,CAAC;EACrD;EACA1J,mBAAmB,CAAC;IAChBwJ,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,UAAU;IACjB6C,IAAI,EAAE,kCAAkC;IACxCC,WAAW,EAAE,OAAO;IACpBC,QAAQ,EAAE,yBAAyB;IACnCT,mBAAmB;IACnBM,GAAG;IACH+C,QAAQ,EAAE;MACNC,eAAe,EAAE;QACb5F,KAAK,EAAE,iCAAiC;QACxC5H,IAAI,EAAE,SAAS;QACfyN,YAAY,EAAE;MAClB;MACA;MACA;MACA;MACA;MACA;IACJ;EACJ,CAAC,EAAG7C,GAAG,IAAK;IACR;IACA,MAAMC,GAAG,GAAG,OAAOD,GAAG,CAACC,GAAG,KAAK,UAAU,GAAGD,GAAG,CAACC,GAAG,CAAC6C,IAAI,CAAC9C,GAAG,CAAC,GAAG+C,IAAI,CAAC9C,GAAG;IACxEnD,KAAK,CAACkG,SAAS,CAAC,CAAC;MAAEC,KAAK;MAAEC,OAAO;MAAExN,IAAI;MAAEyN;IAAK,CAAC,KAAK;MAChD,MAAMC,OAAO,GAAGC,eAAe,EAAE;MACjCrD,GAAG,CAACsD,gBAAgB,CAAC;QACjBC,OAAO,EAAEhE,kBAAkB;QAC3BZ,KAAK,EAAE;UACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;UACX9G,KAAK,EAAE,KAAK,GAAGzD,IAAI;UACnB+N,QAAQ,EAAE,OAAO;UACjB/E,IAAI,EAAE;YACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;YAC/BuD,MAAM,EAAEhE,aAAa,CAAC9G,IAAI,CAAC;YAC3ByN;UACJ,CAAC;UACDC;QACJ;MACJ,CAAC,CAAC;MACFH,KAAK,CAAErJ,MAAM,IAAK;QACd8J,YAAY,GAAGC,SAAS;QACxB3D,GAAG,CAACsD,gBAAgB,CAAC;UACjBC,OAAO,EAAEhE,kBAAkB;UAC3BZ,KAAK,EAAE;YACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;YACX9G,KAAK,EAAE,KAAK,GAAGzD,IAAI;YACnB+N,QAAQ,EAAE,KAAK;YACf/E,IAAI,EAAE;cACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;cAC/BuD,MAAM,EAAEhE,aAAa,CAAC9G,IAAI,CAAC;cAC3ByN,IAAI;cACJvJ;YACJ,CAAC;YACDwJ;UACJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACFF,OAAO,CAAE7M,KAAK,IAAK;QACfqN,YAAY,GAAGC,SAAS;QACxB3D,GAAG,CAACsD,gBAAgB,CAAC;UACjBC,OAAO,EAAEhE,kBAAkB;UAC3BZ,KAAK,EAAE;YACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;YACX2D,OAAO,EAAE,OAAO;YAChBzK,KAAK,EAAE,KAAK,GAAGzD,IAAI;YACnB+N,QAAQ,EAAE,KAAK;YACf/E,IAAI,EAAE;cACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;cAC/BuD,MAAM,EAAEhE,aAAa,CAAC9G,IAAI,CAAC;cAC3ByN,IAAI;cACJ9M;YACJ,CAAC;YACD+M;UACJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,CAAC;IACRtG,KAAK,CAACsB,iBAAiB,CAACmD,OAAO,CAAE7L,IAAI,IAAK;MACtClD,KAAK,CAAC,MAAMC,KAAK,CAACqK,KAAK,CAACpH,IAAI,CAAC,CAAC,EAAE,CAACqJ,QAAQ,EAAED,QAAQ,KAAK;QACpDkB,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;QAC3B7D,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;QACpC,IAAIH,gBAAgB,EAAE;UAClBW,GAAG,CAACsD,gBAAgB,CAAC;YACjBC,OAAO,EAAEhE,kBAAkB;YAC3BZ,KAAK,EAAE;cACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;cACX9G,KAAK,EAAE,QAAQ;cACfsK,QAAQ,EAAE/N,IAAI;cACdgJ,IAAI,EAAE;gBACFK,QAAQ;gBACRD;cACJ,CAAC;cACDsE,OAAO,EAAEM;YACb;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,EAAE;QAAEI,IAAI,EAAE;MAAK,CAAC,CAAC;IACtB,CAAC,CAAC;IACFhH,KAAK,CAACiH,UAAU,CAAC,CAAC;MAAEvF,MAAM;MAAEpJ;IAAK,CAAC,EAAE6F,KAAK,KAAK;MAC1C+E,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;MAC3B7D,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;MACpC,IAAI,CAACH,gBAAgB,EACjB;MACJ;MACA,MAAM2E,SAAS,GAAG;QACdR,IAAI,EAAEvD,GAAG,CAAC,CAAC;QACX9G,KAAK,EAAE8F,kBAAkB,CAAC7J,IAAI,CAAC;QAC/BsJ,IAAI,EAAEe,QAAQ,CAAC;UAAE3C,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG;QAAE,CAAC,EAAEsB,eAAe,CAACC,MAAM,CAAC,CAAC;QAC5E4E,OAAO,EAAEM;MACb,CAAC;MACD,IAAItO,IAAI,KAAKZ,YAAY,CAAC2K,aAAa,EAAE;QACrC6E,SAAS,CAACP,QAAQ,GAAG,IAAI;MAC7B,CAAC,MACI,IAAIrO,IAAI,KAAKZ,YAAY,CAAC4K,WAAW,EAAE;QACxC4E,SAAS,CAACP,QAAQ,GAAG,IAAI;MAC7B,CAAC,MACI,IAAIjF,MAAM,IAAI,CAACpB,KAAK,CAACqB,OAAO,CAACD,MAAM,CAAC,EAAE;QACvCwF,SAAS,CAACP,QAAQ,GAAGjF,MAAM,CAACpJ,IAAI;MACpC;MACA,IAAIoJ,MAAM,EAAE;QACRwF,SAAS,CAACtF,IAAI,CAAC,aAAa,CAAC,GAAG;UAC5BhC,OAAO,EAAE;YACLD,OAAO,EAAE,eAAe;YACxBrH,IAAI,EAAE,QAAQ;YACdqL,OAAO,EAAE,qBAAqB;YAC9BvF,KAAK,EAAEsD;UACX;QACJ,CAAC;MACL;MACAwB,GAAG,CAACsD,gBAAgB,CAAC;QACjBC,OAAO,EAAEhE,kBAAkB;QAC3BZ,KAAK,EAAEqF;MACX,CAAC,CAAC;IACN,CAAC,EAAE;MAAEC,QAAQ,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAO,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGrH,KAAK,CAACsH,UAAU;IAClCtH,KAAK,CAACsH,UAAU,GAAG1R,OAAO,CAAE2R,QAAQ,IAAK;MACrCF,SAAS,CAACE,QAAQ,CAAC;MACnBrE,GAAG,CAACsD,gBAAgB,CAAC;QACjBC,OAAO,EAAEhE,kBAAkB;QAC3BZ,KAAK,EAAE;UACH6E,IAAI,EAAEvD,GAAG,CAAC,CAAC;UACX9G,KAAK,EAAE,KAAK,GAAG2D,KAAK,CAACG,GAAG;UACxBwG,QAAQ,EAAE,YAAY;UACtB/E,IAAI,EAAE;YACF5B,KAAK,EAAEN,aAAa,CAACM,KAAK,CAACG,GAAG,CAAC;YAC/BqH,IAAI,EAAE9H,aAAa,CAAC,YAAY;UACpC;QACJ;MACJ,CAAC,CAAC;MACF;MACAwD,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;MAC3B7D,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;MACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;IACxC,CAAC,CAAC;IACF,MAAM;MAAE+E;IAAS,CAAC,GAAGzH,KAAK;IAC1BA,KAAK,CAACyH,QAAQ,GAAG,MAAM;MACnBA,QAAQ,CAAC,CAAC;MACVvE,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;MAC3B7D,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;MACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;MACpCQ,GAAG,CAACwE,WAAW,CAAC,CAAC,CAAC5B,eAAe,IAC7B3I,YAAY,CAAC,aAAa6C,KAAK,CAACG,GAAG,YAAY,CAAC;IACxD,CAAC;IACD;IACA+C,GAAG,CAAC6D,qBAAqB,CAAC,CAAC;IAC3B7D,GAAG,CAACU,iBAAiB,CAAClB,YAAY,CAAC;IACnCQ,GAAG,CAACW,kBAAkB,CAACnB,YAAY,CAAC;IACpCQ,GAAG,CAACwE,WAAW,CAAC,CAAC,CAAC5B,eAAe,IAC7B3I,YAAY,CAAC,IAAI6C,KAAK,CAACG,GAAG,sBAAsB,CAAC;EACzD,CAAC,CAAC;AACN;AACA,IAAIoG,eAAe,GAAG,CAAC;AACvB,IAAIK,YAAY;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sBAAsBA,CAAC3H,KAAK,EAAE4H,WAAW,EAAEC,aAAa,EAAE;EAC/D;EACA,MAAMpE,OAAO,GAAGmE,WAAW,CAAC1G,MAAM,CAAC,CAAC4G,YAAY,EAAEC,UAAU,KAAK;IAC7D;IACAD,YAAY,CAACC,UAAU,CAAC,GAAGtS,KAAK,CAACuK,KAAK,CAAC,CAAC+H,UAAU,CAAC;IACnD,OAAOD,YAAY;EACvB,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,KAAK,MAAMC,UAAU,IAAItE,OAAO,EAAE;IAC9BzD,KAAK,CAAC+H,UAAU,CAAC,GAAG,YAAY;MAC5B;MACA,MAAMC,SAAS,GAAGzB,eAAe;MACjC,MAAM0B,YAAY,GAAGJ,aAAa,GAC5B,IAAIK,KAAK,CAAClI,KAAK,EAAE;QACfgB,GAAGA,CAAC,GAAGqF,IAAI,EAAE;UACTO,YAAY,GAAGoB,SAAS;UACxB,OAAOG,OAAO,CAACnH,GAAG,CAAC,GAAGqF,IAAI,CAAC;QAC/B,CAAC;QACDZ,GAAGA,CAAC,GAAGY,IAAI,EAAE;UACTO,YAAY,GAAGoB,SAAS;UACxB,OAAOG,OAAO,CAAC1C,GAAG,CAAC,GAAGY,IAAI,CAAC;QAC/B;MACJ,CAAC,CAAC,GACArG,KAAK;MACX;MACA4G,YAAY,GAAGoB,SAAS;MACxB,MAAMI,QAAQ,GAAG3E,OAAO,CAACsE,UAAU,CAAC,CAACM,KAAK,CAACJ,YAAY,EAAEK,SAAS,CAAC;MACnE;MACA1B,YAAY,GAAGC,SAAS;MACxB,OAAOuB,QAAQ;IACnB,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA,SAASG,cAAcA,CAAC;EAAEzF,GAAG;EAAE9C,KAAK;EAAEwI;AAAQ,CAAC,EAAE;EAC7C;EACA,IAAIxI,KAAK,CAACG,GAAG,CAACwF,UAAU,CAAC,QAAQ,CAAC,EAAE;IAChC;EACJ;EACA;EACA3F,KAAK,CAAC2E,aAAa,GAAG,CAAC,CAAC6D,OAAO,CAACrK,KAAK;EACrC;EACA,IAAI,CAAC6B,KAAK,CAACyI,EAAE,CAACC,QAAQ,EAAE;IACpBf,sBAAsB,CAAC3H,KAAK,EAAE3I,MAAM,CAACoJ,IAAI,CAAC+H,OAAO,CAAC/E,OAAO,CAAC,EAAEzD,KAAK,CAAC2E,aAAa,CAAC;IAChF;IACA,MAAMgE,iBAAiB,GAAG3I,KAAK,CAACsH,UAAU;IAC1C7R,KAAK,CAACuK,KAAK,CAAC,CAACsH,UAAU,GAAG,UAAUC,QAAQ,EAAE;MAC1CoB,iBAAiB,CAACN,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACxCX,sBAAsB,CAAC3H,KAAK,EAAE3I,MAAM,CAACoJ,IAAI,CAAC8G,QAAQ,CAACqB,WAAW,CAACnF,OAAO,CAAC,EAAE,CAAC,CAACzD,KAAK,CAAC2E,aAAa,CAAC;IACnG,CAAC;EACL;EACAiB,kBAAkB,CAAC9C,GAAG;EACtB;EACA9C,KAAK,CAAC;AACV;;AAEA;AACA;AACA;AACA,SAAS6I,WAAWA,CAAA,EAAG;EACnB,MAAMC,KAAK,GAAGjT,WAAW,CAAC,IAAI,CAAC;EAC/B;EACA;EACA,MAAMsI,KAAK,GAAG2K,KAAK,CAACC,GAAG,CAAC,MAAMjT,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC,IAAI2S,EAAE,GAAG,EAAE;EACX;EACA,IAAIO,aAAa,GAAG,EAAE;EACtB,MAAMpS,KAAK,GAAGhB,OAAO,CAAC;IAClBqT,OAAOA,CAACnG,GAAG,EAAE;MACT;MACA;MACAnM,cAAc,CAACC,KAAK,CAAC;MACrBA,KAAK,CAACsS,EAAE,GAAGpG,GAAG;MACdA,GAAG,CAACqG,OAAO,CAACrS,WAAW,EAAEF,KAAK,CAAC;MAC/BkM,GAAG,CAACsG,MAAM,CAACC,gBAAgB,CAACpE,MAAM,GAAGrO,KAAK;MAC1C;MACA,IAAK,CAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOqS,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAEvS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;QAC3KkL,qBAAqB,CAACC,GAAG,EAAElM,KAAK,CAAC;MACrC;MACAoS,aAAa,CAACvE,OAAO,CAAE8E,MAAM,IAAKd,EAAE,CAAC3G,IAAI,CAACyH,MAAM,CAAC,CAAC;MAClDP,aAAa,GAAG,EAAE;IACtB,CAAC;IACDQ,GAAGA,CAACD,MAAM,EAAE;MACR,IAAI,CAAC,IAAI,CAACL,EAAE,EAAE;QACVF,aAAa,CAAClH,IAAI,CAACyH,MAAM,CAAC;MAC9B,CAAC,MACI;QACDd,EAAE,CAAC3G,IAAI,CAACyH,MAAM,CAAC;MACnB;MACA,OAAO,IAAI;IACf,CAAC;IACDd,EAAE;IACF;IACA;IACAS,EAAE,EAAE,IAAI;IACRO,EAAE,EAAEX,KAAK;IACTtI,EAAE,EAAE,IAAIkJ,GAAG,CAAC,CAAC;IACbvL;EACJ,CAAC,CAAC;EACF;EACA;EACA,IAAK,CAAEpH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOqS,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAEvS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,IAAI,OAAOuQ,KAAK,KAAK,WAAW,EAAE;IAC3MtR,KAAK,CAAC4S,GAAG,CAACjB,cAAc,CAAC;EAC7B;EACA,OAAO3R,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+S,YAAYA,CAAC/S,KAAK,EAAE;EACzBA,KAAK,CAAC6S,EAAE,CAACG,IAAI,CAAC,CAAC;EACfhT,KAAK,CAAC4J,EAAE,CAACqJ,KAAK,CAAC,CAAC;EAChBjT,KAAK,CAAC6R,EAAE,CAACqB,MAAM,CAAC,CAAC,CAAC;EAClBlT,KAAK,CAACuH,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC;EACtB;EACAxH,KAAK,CAACsS,EAAE,GAAG,IAAI;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,UAAU,GAAIC,EAAE,IAAK;EACvB,OAAO,OAAOA,EAAE,KAAK,UAAU,IAAI,OAAOA,EAAE,CAAC7J,GAAG,KAAK,QAAQ;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,WAAWA,CAAC2H,QAAQ,EAAEC,QAAQ,EAAE;EACrC;EACA,KAAK,MAAM1K,GAAG,IAAI0K,QAAQ,EAAE;IACxB,MAAMC,QAAQ,GAAGD,QAAQ,CAAC1K,GAAG,CAAC;IAC9B;IACA,IAAI,EAAEA,GAAG,IAAIyK,QAAQ,CAAC,EAAE;MACpB;IACJ;IACA,MAAMG,WAAW,GAAGH,QAAQ,CAACzK,GAAG,CAAC;IACjC,IAAIrI,aAAa,CAACiT,WAAW,CAAC,IAC1BjT,aAAa,CAACgT,QAAQ,CAAC,IACvB,CAACpU,KAAK,CAACoU,QAAQ,CAAC,IAChB,CAACnU,UAAU,CAACmU,QAAQ,CAAC,EAAE;MACvBF,QAAQ,CAACzK,GAAG,CAAC,GAAG8C,WAAW,CAAC8H,WAAW,EAAED,QAAQ,CAAC;IACtD,CAAC,MACI;MACD;MACA;MACAF,QAAQ,CAACzK,GAAG,CAAC,GAAG2K,QAAQ;IAC5B;EACJ;EACA,OAAOF,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAACC,eAAe,EAAEC,GAAG,EAAE;EAC3C;EACA,IAAI,EAAExT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,EAAE;IAC1C,OAAO,MAAM,CAAE,CAAC;EACpB;EACA,OAAQuT,SAAS,IAAK;IAClB,MAAM5T,KAAK,GAAG2T,GAAG,CAAC3I,IAAI,CAAChL,KAAK,IAAI0T,eAAe,CAACG,MAAM;IACtD,IAAI,CAAC7T,KAAK,EAAE;MACR;MACA;IACJ;IACA;IACA2T,GAAG,CAAC3I,IAAI,CAAChL,KAAK,GAAGA,KAAK;IACtB;IACA,KAAK,MAAM8T,UAAU,IAAIF,SAAS,EAAE;MAChC,MAAMG,QAAQ,GAAGH,SAAS,CAACE,UAAU,CAAC;MACtC;MACA,IAAIX,UAAU,CAACY,QAAQ,CAAC,IAAI/T,KAAK,CAAC4J,EAAE,CAAC+E,GAAG,CAACoF,QAAQ,CAACxK,GAAG,CAAC,EAAE;QACpD;QACA,MAAMF,EAAE,GAAG0K,QAAQ,CAACxK,GAAG;QACvB,IAAIF,EAAE,KAAKqK,eAAe,CAACnK,GAAG,EAAE;UAC5B7G,OAAO,CAACiE,IAAI,CAAC,qCAAqC+M,eAAe,CAACnK,GAAG,SAASF,EAAE,eAAe,CAAC;UAChG;UACA,OAAOsK,GAAG,CAACK,UAAU,CAAC,CAAC;QAC3B;QACA,MAAMC,aAAa,GAAGjU,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAACf,EAAE,CAAC;QACtC,IAAI,CAAC4K,aAAa,EAAE;UAChBvR,OAAO,CAACkE,GAAG,CAAC,uDAAuD,CAAC;UACpE;QACJ;QACAmN,QAAQ,CAAC/T,KAAK,EAAEiU,aAAa,CAAC;MAClC;IACJ;EACJ,CAAC;AACL;AAEA,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAE,CAAC;AACtB,SAASC,eAAeA,CAACC,aAAa,EAAEC,QAAQ,EAAE9D,QAAQ,EAAE+D,SAAS,GAAGJ,IAAI,EAAE;EAC1EE,aAAa,CAAClJ,IAAI,CAACmJ,QAAQ,CAAC;EAC5B,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,GAAG,GAAGJ,aAAa,CAACK,OAAO,CAACJ,QAAQ,CAAC;IAC3C,IAAIG,GAAG,GAAG,CAAC,CAAC,EAAE;MACVJ,aAAa,CAAClB,MAAM,CAACsB,GAAG,EAAE,CAAC,CAAC;MAC5BF,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EACD,IAAI,CAAC/D,QAAQ,IAAIlR,eAAe,CAAC,CAAC,EAAE;IAChCC,cAAc,CAACiV,kBAAkB,CAAC;EACtC;EACA,OAAOA,kBAAkB;AAC7B;AACA,SAASG,oBAAoBA,CAACN,aAAa,EAAE,GAAG3E,IAAI,EAAE;EAClD2E,aAAa,CAACO,KAAK,CAAC,CAAC,CAAC9G,OAAO,CAAEwG,QAAQ,IAAK;IACxCA,QAAQ,CAAC,GAAG5E,IAAI,CAAC;EACrB,CAAC,CAAC;AACN;AAEA,MAAMmF,sBAAsB,GAAIxB,EAAE,IAAKA,EAAE,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA,MAAMyB,aAAa,GAAGvU,MAAM,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA,MAAMwU,WAAW,GAAGxU,MAAM,CAAC,CAAC;AAC5B,SAASyU,oBAAoBA,CAAC7P,MAAM,EAAE8P,YAAY,EAAE;EAChD;EACA,IAAI9P,MAAM,YAAY4N,GAAG,IAAIkC,YAAY,YAAYlC,GAAG,EAAE;IACtDkC,YAAY,CAACnH,OAAO,CAAC,CAACrG,KAAK,EAAEoB,GAAG,KAAK1D,MAAM,CAAC2J,GAAG,CAACjG,GAAG,EAAEpB,KAAK,CAAC,CAAC;EAChE,CAAC,MACI,IAAItC,MAAM,YAAY+P,GAAG,IAAID,YAAY,YAAYC,GAAG,EAAE;IAC3D;IACAD,YAAY,CAACnH,OAAO,CAAC3I,MAAM,CAACgQ,GAAG,EAAEhQ,MAAM,CAAC;EAC5C;EACA;EACA,KAAK,MAAM0D,GAAG,IAAIoM,YAAY,EAAE;IAC5B,IAAI,CAACA,YAAY,CAACG,cAAc,CAACvM,GAAG,CAAC,EACjC;IACJ,MAAM2K,QAAQ,GAAGyB,YAAY,CAACpM,GAAG,CAAC;IAClC,MAAM4K,WAAW,GAAGtO,MAAM,CAAC0D,GAAG,CAAC;IAC/B,IAAIrI,aAAa,CAACiT,WAAW,CAAC,IAC1BjT,aAAa,CAACgT,QAAQ,CAAC,IACvBrO,MAAM,CAACiQ,cAAc,CAACvM,GAAG,CAAC,IAC1B,CAACzJ,KAAK,CAACoU,QAAQ,CAAC,IAChB,CAACnU,UAAU,CAACmU,QAAQ,CAAC,EAAE;MACvB;MACA;MACA;MACArO,MAAM,CAAC0D,GAAG,CAAC,GAAGmM,oBAAoB,CAACvB,WAAW,EAAED,QAAQ,CAAC;IAC7D,CAAC,MACI;MACD;MACArO,MAAM,CAAC0D,GAAG,CAAC,GAAG2K,QAAQ;IAC1B;EACJ;EACA,OAAOrO,MAAM;AACjB;AACA,MAAMkQ,iBAAiB,GAAIjV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAC1DC,MAAM,CAAC,qBAAqB,CAAC,GAC7B,0BAA2BA,MAAM,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+U,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAO7U,MAAM,CAAC8U,cAAc,CAACD,GAAG,EAAEF,iBAAiB,EAAE,CAAC,CAAC,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACF,GAAG,EAAE;EACxB,OAAQ,CAAC/U,aAAa,CAAC+U,GAAG,CAAC,IACvB,CAAC7U,MAAM,CAACC,SAAS,CAACyU,cAAc,CAACvU,IAAI,CAAC0U,GAAG,EAAEF,iBAAiB,CAAC;AACrE;AACA,MAAM;EAAE/O;AAAO,CAAC,GAAG5F,MAAM;AACzB,SAASgV,UAAUA,CAACjV,CAAC,EAAE;EACnB,OAAO,CAAC,EAAErB,KAAK,CAACqB,CAAC,CAAC,IAAIA,CAAC,CAACkV,MAAM,CAAC;AACnC;AACA,SAASC,kBAAkBA,CAACtM,EAAE,EAAEuI,OAAO,EAAE5R,KAAK,EAAE2T,GAAG,EAAE;EACjD,MAAM;IAAEpM,KAAK;IAAEsF,OAAO;IAAE3C;EAAQ,CAAC,GAAG0H,OAAO;EAC3C,MAAMgE,YAAY,GAAG5V,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC6B,EAAE,CAAC;EAC1C,IAAID,KAAK;EACT,SAASyM,KAAKA,CAAA,EAAG;IACb,IAAI,CAACD,YAAY,KAAK,EAAEzV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,IAAI,CAACsT,GAAG,CAAC,EAAE;MACrE;MACA3T,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC6B,EAAE,CAAC,GAAG9B,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD;IACA;IACA,MAAMuO,UAAU,GAAI3V,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKsT,GAAG;IAC3D;IACE/T,MAAM,CAACV,GAAG,CAACqI,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GACzC5H,MAAM,CAACI,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC6B,EAAE,CAAC,CAAC;IACnC,OAAOhD,MAAM,CAACyP,UAAU,EAAEjJ,OAAO,EAAEpM,MAAM,CAACoJ,IAAI,CAACK,OAAO,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAACyL,eAAe,EAAE/T,IAAI,KAAK;MAC5F,IAAK7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK2B,IAAI,IAAI8T,UAAU,EAAE;QAC/DpT,OAAO,CAACiE,IAAI,CAAC,uGAAuG3E,IAAI,eAAeqH,EAAE,IAAI,CAAC;MAClJ;MACA0M,eAAe,CAAC/T,IAAI,CAAC,GAAGhD,OAAO,CAACW,QAAQ,CAAC,MAAM;QAC3CI,cAAc,CAACC,KAAK,CAAC;QACrB;QACA,MAAMoJ,KAAK,GAAGpJ,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAACf,EAAE,CAAC;QAC9B;QACA;QACA;QACA;QACA,OAAOa,OAAO,CAAClI,IAAI,CAAC,CAACpB,IAAI,CAACwI,KAAK,EAAEA,KAAK,CAAC;MAC3C,CAAC,CAAC,CAAC;MACH,OAAO2M,eAAe;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX;EACA3M,KAAK,GAAG4M,gBAAgB,CAAC3M,EAAE,EAAEwM,KAAK,EAAEjE,OAAO,EAAE5R,KAAK,EAAE2T,GAAG,EAAE,IAAI,CAAC;EAC9D,OAAOvK,KAAK;AAChB;AACA,SAAS4M,gBAAgBA,CAACzM,GAAG,EAAEsM,KAAK,EAAEjE,OAAO,GAAG,CAAC,CAAC,EAAE5R,KAAK,EAAE2T,GAAG,EAAEsC,cAAc,EAAE;EAC5E,IAAI/D,KAAK;EACT,MAAMgE,gBAAgB,GAAG7P,MAAM,CAAC;IAAEwG,OAAO,EAAE,CAAC;EAAE,CAAC,EAAE+E,OAAO,CAAC;EACzD;EACA,IAAKzR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACL,KAAK,CAAC6S,EAAE,CAACsD,MAAM,EAAE;IAC7D,MAAM,IAAIhQ,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA;EACA,MAAMiQ,iBAAiB,GAAG;IAAEhG,IAAI,EAAE;EAAK,CAAC;EACxC;EACA,IAAKjQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;IACzC+V,iBAAiB,CAACC,SAAS,GAAIpL,KAAK,IAAK;MACrC;MACA,IAAIqL,WAAW,EAAE;QACbC,cAAc,GAAGtL,KAAK;QACtB;MACJ,CAAC,MACI,IAAIqL,WAAW,IAAI,KAAK,IAAI,CAAClN,KAAK,CAACoN,YAAY,EAAE;QAClD;QACA;QACA,IAAI9M,KAAK,CAACqB,OAAO,CAACwL,cAAc,CAAC,EAAE;UAC/BA,cAAc,CAACrL,IAAI,CAACD,KAAK,CAAC;QAC9B,CAAC,MACI;UACDvI,OAAO,CAACC,KAAK,CAAC,kFAAkF,CAAC;QACrG;MACJ;IACJ,CAAC;EACL;EACA;EACA,IAAI2T,WAAW,CAAC,CAAC;EACjB,IAAIG,eAAe,CAAC,CAAC;EACrB,IAAIrC,aAAa,GAAG,EAAE;EACtB,IAAIsC,mBAAmB,GAAG,EAAE;EAC5B,IAAIH,cAAc;EAClB,MAAMX,YAAY,GAAG5V,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC;EAC3C;EACA;EACA,IAAI,CAAC0M,cAAc,IAAI,CAACL,YAAY,KAAK,EAAEzV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,IAAI,CAACsT,GAAG,CAAC,EAAE;IACxF;IACA3T,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,GAAG,CAAC,CAAC;EAC/B;EACA,MAAMoN,QAAQ,GAAGzX,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB;EACA;EACA,IAAI0X,cAAc;EAClB,SAASC,MAAMA,CAACC,qBAAqB,EAAE;IACnC,IAAIC,oBAAoB;IACxBT,WAAW,GAAGG,eAAe,GAAG,KAAK;IACrC;IACA;IACA,IAAKtW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MACzCkW,cAAc,GAAG,EAAE;IACvB;IACA,IAAI,OAAOO,qBAAqB,KAAK,UAAU,EAAE;MAC7CA,qBAAqB,CAAC9W,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAAC;MAC7CwN,oBAAoB,GAAG;QACnBrV,IAAI,EAAEZ,YAAY,CAAC2K,aAAa;QAChCzB,OAAO,EAAET,GAAG;QACZuB,MAAM,EAAEyL;MACZ,CAAC;IACL,CAAC,MACI;MACDxB,oBAAoB,CAAC/U,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,EAAEuN,qBAAqB,CAAC;MACnEC,oBAAoB,GAAG;QACnBrV,IAAI,EAAEZ,YAAY,CAAC4K,WAAW;QAC9B6B,OAAO,EAAEuJ,qBAAqB;QAC9B9M,OAAO,EAAET,GAAG;QACZuB,MAAM,EAAEyL;MACZ,CAAC;IACL;IACA,MAAMS,YAAY,GAAIJ,cAAc,GAAGtW,MAAM,CAAC,CAAE;IAChDZ,QAAQ,CAAC,CAAC,CAACuX,IAAI,CAAC,MAAM;MAClB,IAAIL,cAAc,KAAKI,YAAY,EAAE;QACjCV,WAAW,GAAG,IAAI;MACtB;IACJ,CAAC,CAAC;IACFG,eAAe,GAAG,IAAI;IACtB;IACA/B,oBAAoB,CAACN,aAAa,EAAE2C,oBAAoB,EAAE/W,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAAC;EACrF;EACA,MAAM6D,MAAM,GAAG6I,cAAc,GACvB,SAAS7I,MAAMA,CAAA,EAAG;IAChB,MAAM;MAAE7F;IAAM,CAAC,GAAGqK,OAAO;IACzB,MAAMyB,QAAQ,GAAG9L,KAAK,GAAGA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IACrC;IACA,IAAI,CAACsP,MAAM,CAAEtM,MAAM,IAAK;MACpB;MACAlE,MAAM,CAACkE,MAAM,EAAE8I,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACN,CAAC,GACC;EACGlT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAChC,MAAM;IACJ,MAAM,IAAI8F,KAAK,CAAC,cAAcoD,GAAG,oEAAoE,CAAC;EAC1G,CAAC,GACC2K,IAAI;EAClB,SAASrD,QAAQA,CAAA,EAAG;IAChBqB,KAAK,CAACc,IAAI,CAAC,CAAC;IACZoB,aAAa,GAAG,EAAE;IAClBsC,mBAAmB,GAAG,EAAE;IACxB1W,KAAK,CAAC4J,EAAE,CAACsN,MAAM,CAAC3N,GAAG,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI,MAAMuD,MAAM,GAAGA,CAACsG,EAAE,EAAEpR,IAAI,GAAG,EAAE,KAAK;IAC9B,IAAI6S,aAAa,IAAIzB,EAAE,EAAE;MACrBA,EAAE,CAAC0B,WAAW,CAAC,GAAG9S,IAAI;MACtB,OAAOoR,EAAE;IACb;IACA,MAAM+D,aAAa,GAAG,SAAAA,CAAA,EAAY;MAC9BpX,cAAc,CAACC,KAAK,CAAC;MACrB,MAAMyP,IAAI,GAAG/F,KAAK,CAACC,IAAI,CAAC+H,SAAS,CAAC;MAClC,MAAM0F,iBAAiB,GAAG,EAAE;MAC5B,MAAMC,mBAAmB,GAAG,EAAE;MAC9B,SAAS9H,KAAKA,CAAC8E,QAAQ,EAAE;QACrB+C,iBAAiB,CAAClM,IAAI,CAACmJ,QAAQ,CAAC;MACpC;MACA,SAAS7E,OAAOA,CAAC6E,QAAQ,EAAE;QACvBgD,mBAAmB,CAACnM,IAAI,CAACmJ,QAAQ,CAAC;MACtC;MACA;MACAK,oBAAoB,CAACgC,mBAAmB,EAAE;QACtCjH,IAAI;QACJzN,IAAI,EAAEmV,aAAa,CAACrC,WAAW,CAAC;QAChC1L,KAAK;QACLmG,KAAK;QACLC;MACJ,CAAC,CAAC;MACF,IAAI8H,GAAG;MACP,IAAI;QACAA,GAAG,GAAGlE,EAAE,CAAC3B,KAAK,CAAC,IAAI,IAAI,IAAI,CAAClI,GAAG,KAAKA,GAAG,GAAG,IAAI,GAAGH,KAAK,EAAEqG,IAAI,CAAC;QAC7D;MACJ,CAAC,CACD,OAAO9M,KAAK,EAAE;QACV+R,oBAAoB,CAAC2C,mBAAmB,EAAE1U,KAAK,CAAC;QAChD,MAAMA,KAAK;MACf;MACA,IAAI2U,GAAG,YAAYpP,OAAO,EAAE;QACxB,OAAOoP,GAAG,CACLL,IAAI,CAAEzP,KAAK,IAAK;UACjBkN,oBAAoB,CAAC0C,iBAAiB,EAAE5P,KAAK,CAAC;UAC9C,OAAOA,KAAK;QAChB,CAAC,CAAC,CACG+P,KAAK,CAAE5U,KAAK,IAAK;UAClB+R,oBAAoB,CAAC2C,mBAAmB,EAAE1U,KAAK,CAAC;UAChD,OAAOuF,OAAO,CAACE,MAAM,CAACzF,KAAK,CAAC;QAChC,CAAC,CAAC;MACN;MACA;MACA+R,oBAAoB,CAAC0C,iBAAiB,EAAEE,GAAG,CAAC;MAC5C,OAAOA,GAAG;IACd,CAAC;IACDH,aAAa,CAACtC,aAAa,CAAC,GAAG,IAAI;IACnCsC,aAAa,CAACrC,WAAW,CAAC,GAAG9S,IAAI,CAAC,CAAC;IACnC;IACA;IACA,OAAOmV,aAAa;EACxB,CAAC;EACD,MAAMnF,WAAW,GAAG,aAAchT,OAAO,CAAC;IACtC6N,OAAO,EAAE,CAAC,CAAC;IACX3C,OAAO,EAAE,CAAC,CAAC;IACX3C,KAAK,EAAE,EAAE;IACToP;EACJ,CAAC,CAAC;EACF,MAAMa,YAAY,GAAG;IACjB3F,EAAE,EAAE7R,KAAK;IACT;IACAuJ,GAAG;IACH+F,SAAS,EAAE6E,eAAe,CAAC/E,IAAI,CAAC,IAAI,EAAEsH,mBAAmB,CAAC;IAC1DG,MAAM;IACNzJ,MAAM;IACNiD,UAAUA,CAACgE,QAAQ,EAAEzC,OAAO,GAAG,CAAC,CAAC,EAAE;MAC/B,MAAM2C,kBAAkB,GAAGJ,eAAe,CAACC,aAAa,EAAEC,QAAQ,EAAEzC,OAAO,CAACrB,QAAQ,EAAE,MAAMkH,WAAW,CAAC,CAAC,CAAC;MAC1G,MAAMA,WAAW,GAAGvF,KAAK,CAACC,GAAG,CAAC,MAAMrT,KAAK,CAAC,MAAMkB,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,EAAGhC,KAAK,IAAK;QAC/E,IAAIqK,OAAO,CAACpB,KAAK,KAAK,MAAM,GAAGiG,eAAe,GAAGH,WAAW,EAAE;UAC1DjC,QAAQ,CAAC;YACLrK,OAAO,EAAET,GAAG;YACZ7H,IAAI,EAAEZ,YAAY,CAAC0K,MAAM;YACzBV,MAAM,EAAEyL;UACZ,CAAC,EAAEhP,KAAK,CAAC;QACb;MACJ,CAAC,EAAElB,MAAM,CAAC,CAAC,CAAC,EAAE+P,iBAAiB,EAAExE,OAAO,CAAC,CAAC,CAAC;MAC3C,OAAO2C,kBAAkB;IAC7B,CAAC;IACD1D;EACJ,CAAC;EACD,MAAMzH,KAAK,GAAG5J,QAAQ,CAAEW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAO,CAAEF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOqS,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAEvS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAU,GACvOsF,MAAM,CAAC;IACL2L,WAAW;IACXtH,iBAAiB,EAAE1L,OAAO,CAAC,IAAIiW,GAAG,CAAC,CAAC,CAAC,CAAE;EAC3C,CAAC,EAAEuC;EACH;EACA;EACA,CAAC,GACCA,YAAY,CAAC;EACnB;EACA;EACAxX,KAAK,CAAC4J,EAAE,CAACiF,GAAG,CAACtF,GAAG,EAAEH,KAAK,CAAC;EACxB,MAAMsO,cAAc,GAAI1X,KAAK,CAACsS,EAAE,IAAItS,KAAK,CAACsS,EAAE,CAACoF,cAAc,IAAK9C,sBAAsB;EACtF;EACA,MAAM+C,UAAU,GAAGD,cAAc,CAAC,MAAM1X,KAAK,CAAC6S,EAAE,CAACV,GAAG,CAAC,MAAM,CAACD,KAAK,GAAGjT,WAAW,CAAC,CAAC,EAAEkT,GAAG,CAAC,MAAM0D,KAAK,CAAC;IAAE/I;EAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACjH;EACA,KAAK,MAAMlE,GAAG,IAAI+O,UAAU,EAAE;IAC1B,MAAMC,IAAI,GAAGD,UAAU,CAAC/O,GAAG,CAAC;IAC5B,IAAKzJ,KAAK,CAACyY,IAAI,CAAC,IAAI,CAACnC,UAAU,CAACmC,IAAI,CAAC,IAAKxY,UAAU,CAACwY,IAAI,CAAC,EAAE;MACxD;MACA,IAAKzX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKsT,GAAG,EAAE;QAChDgD,QAAQ,CAACnP,KAAK,CAACoB,GAAG,CAAC,GAAGnJ,KAAK,CAACkY,UAAU,EAAE/O,GAAG,CAAC;QAC5C;QACA;MACJ,CAAC,MACI,IAAI,CAACqN,cAAc,EAAE;QACtB;QACA,IAAIL,YAAY,IAAIJ,aAAa,CAACoC,IAAI,CAAC,EAAE;UACrC,IAAIzY,KAAK,CAACyY,IAAI,CAAC,EAAE;YACbA,IAAI,CAACpQ,KAAK,GAAGoO,YAAY,CAAChN,GAAG,CAAC;UAClC,CAAC,MACI;YACD;YACA;YACAmM,oBAAoB,CAAC6C,IAAI,EAAEhC,YAAY,CAAChN,GAAG,CAAC,CAAC;UACjD;QACJ;QACA;QACA5I,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,CAACX,GAAG,CAAC,GAAGgP,IAAI;MACtC;MACA;MACA,IAAKzX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC2R,WAAW,CAACzK,KAAK,CAAC2D,IAAI,CAACtC,GAAG,CAAC;MAC/B;MACA;IACJ,CAAC,MACI,IAAI,OAAOgP,IAAI,KAAK,UAAU,EAAE;MACjC,MAAMC,WAAW,GAAI1X,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKsT,GAAG,GAAGiE,IAAI,GAAG9K,MAAM,CAAC8K,IAAI,EAAEhP,GAAG,CAAC;MAC7F;MACA;MACA;MACA+O,UAAU,CAAC/O,GAAG,CAAC,GAAGiP,WAAW;MAC7B;MACA,IAAK1X,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC2R,WAAW,CAACnF,OAAO,CAACjE,GAAG,CAAC,GAAGgP,IAAI;MACnC;MACA;MACA;MACA1B,gBAAgB,CAACrJ,OAAO,CAACjE,GAAG,CAAC,GAAGgP,IAAI;IACxC,CAAC,MACI,IAAKzX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;MAC9C;MACA,IAAIoV,UAAU,CAACmC,IAAI,CAAC,EAAE;QAClB5F,WAAW,CAAC9H,OAAO,CAACtB,GAAG,CAAC,GAAGqN,cAAc;QACnC;QACErE,OAAO,CAAC1H,OAAO,CAACtB,GAAG,CAAC,GACtBgP,IAAI;QACV,IAAI7W,SAAS,EAAE;UACX,MAAMmJ,OAAO,GAAGyN,UAAU,CAACtN,QAAQ;UAC/B;UACCsN,UAAU,CAACtN,QAAQ,GAAGrL,OAAO,CAAC,EAAE,CAAC,CAAC;UACvCkL,OAAO,CAACgB,IAAI,CAACtC,GAAG,CAAC;QACrB;MACJ;IACJ;EACJ;EACA;EACA;EACAvC,MAAM,CAAC+C,KAAK,EAAEuO,UAAU,CAAC;EACzB;EACA;EACAtR,MAAM,CAACxH,KAAK,CAACuK,KAAK,CAAC,EAAEuO,UAAU,CAAC;EAChC;EACA;EACA;EACAlX,MAAM,CAAC8U,cAAc,CAACnM,KAAK,EAAE,QAAQ,EAAE;IACnCgB,GAAG,EAAEA,CAAA,KAAQjK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKsT,GAAG,GAAGgD,QAAQ,CAACnP,KAAK,GAAGxH,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAE;IACrGsF,GAAG,EAAGtH,KAAK,IAAK;MACZ;MACA,IAAKpH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKsT,GAAG,EAAE;QAChD,MAAM,IAAIxN,KAAK,CAAC,qBAAqB,CAAC;MAC1C;MACA0Q,MAAM,CAAEtM,MAAM,IAAK;QACf;QACAlE,MAAM,CAACkE,MAAM,EAAEhD,KAAK,CAAC;MACzB,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF;EACA;EACA,IAAKpH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;IACzC+I,KAAK,CAACsH,UAAU,GAAG1R,OAAO,CAAE2R,QAAQ,IAAK;MACrCvH,KAAK,CAACoN,YAAY,GAAG,IAAI;MACzB7F,QAAQ,CAACqB,WAAW,CAACzK,KAAK,CAACsG,OAAO,CAAEiK,QAAQ,IAAK;QAC7C,IAAIA,QAAQ,IAAI1O,KAAK,CAACmB,MAAM,EAAE;UAC1B,MAAMwN,cAAc,GAAGpH,QAAQ,CAACpG,MAAM,CAACuN,QAAQ,CAAC;UAChD,MAAME,cAAc,GAAG5O,KAAK,CAACmB,MAAM,CAACuN,QAAQ,CAAC;UAC7C,IAAI,OAAOC,cAAc,KAAK,QAAQ,IAClCxX,aAAa,CAACwX,cAAc,CAAC,IAC7BxX,aAAa,CAACyX,cAAc,CAAC,EAAE;YAC/BtM,WAAW,CAACqM,cAAc,EAAEC,cAAc,CAAC;UAC/C,CAAC,MACI;YACD;YACArH,QAAQ,CAACpG,MAAM,CAACuN,QAAQ,CAAC,GAAGE,cAAc;UAC9C;QACJ;QACA;QACA;QACA;QACA5O,KAAK,CAAC0O,QAAQ,CAAC,GAAGrY,KAAK,CAACkR,QAAQ,CAACpG,MAAM,EAAEuN,QAAQ,CAAC;MACtD,CAAC,CAAC;MACF;MACArX,MAAM,CAACoJ,IAAI,CAACT,KAAK,CAACmB,MAAM,CAAC,CAACsD,OAAO,CAAEiK,QAAQ,IAAK;QAC5C,IAAI,EAAEA,QAAQ,IAAInH,QAAQ,CAACpG,MAAM,CAAC,EAAE;UAChC;UACA,OAAOnB,KAAK,CAAC0O,QAAQ,CAAC;QAC1B;MACJ,CAAC,CAAC;MACF;MACAxB,WAAW,GAAG,KAAK;MACnBG,eAAe,GAAG,KAAK;MACvBzW,KAAK,CAACuH,KAAK,CAACC,KAAK,CAAC+B,GAAG,CAAC,GAAG9J,KAAK,CAACkR,QAAQ,CAACqB,WAAW,EAAE,UAAU,CAAC;MAChEyE,eAAe,GAAG,IAAI;MACtB/W,QAAQ,CAAC,CAAC,CAACuX,IAAI,CAAC,MAAM;QAClBX,WAAW,GAAG,IAAI;MACtB,CAAC,CAAC;MACF,KAAK,MAAMnF,UAAU,IAAIR,QAAQ,CAACqB,WAAW,CAACnF,OAAO,EAAE;QACnD,MAAMoL,QAAQ,GAAGtH,QAAQ,CAACQ,UAAU,CAAC;QACrC;QACA/H,KAAK,CAAC+H,UAAU,CAAC;QACb;QACArE,MAAM,CAACmL,QAAQ,EAAE9G,UAAU,CAAC;MACpC;MACA;MACA,KAAK,MAAM1G,UAAU,IAAIkG,QAAQ,CAACqB,WAAW,CAAC9H,OAAO,EAAE;QACnD,MAAMgO,MAAM,GAAGvH,QAAQ,CAACqB,WAAW,CAAC9H,OAAO,CAACO,UAAU,CAAC;QACvD,MAAM0N,WAAW,GAAGlC,cAAc;QAC5B;QACEtW,QAAQ,CAAC,MAAM;UACXI,cAAc,CAACC,KAAK,CAAC;UACrB,OAAOkY,MAAM,CAACtX,IAAI,CAACwI,KAAK,EAAEA,KAAK,CAAC;QACpC,CAAC,CAAC,GACJ8O,MAAM;QACZ;QACA9O,KAAK,CAACqB,UAAU,CAAC;QACb;QACA0N,WAAW;MACnB;MACA;MACA1X,MAAM,CAACoJ,IAAI,CAACT,KAAK,CAAC4I,WAAW,CAAC9H,OAAO,CAAC,CAAC2D,OAAO,CAAEjF,GAAG,IAAK;QACpD,IAAI,EAAEA,GAAG,IAAI+H,QAAQ,CAACqB,WAAW,CAAC9H,OAAO,CAAC,EAAE;UACxC;UACA,OAAOd,KAAK,CAACR,GAAG,CAAC;QACrB;MACJ,CAAC,CAAC;MACF;MACAnI,MAAM,CAACoJ,IAAI,CAACT,KAAK,CAAC4I,WAAW,CAACnF,OAAO,CAAC,CAACgB,OAAO,CAAEjF,GAAG,IAAK;QACpD,IAAI,EAAEA,GAAG,IAAI+H,QAAQ,CAACqB,WAAW,CAACnF,OAAO,CAAC,EAAE;UACxC;UACA,OAAOzD,KAAK,CAACR,GAAG,CAAC;QACrB;MACJ,CAAC,CAAC;MACF;MACAQ,KAAK,CAAC4I,WAAW,GAAGrB,QAAQ,CAACqB,WAAW;MACxC5I,KAAK,CAACiB,QAAQ,GAAGsG,QAAQ,CAACtG,QAAQ;MAClCjB,KAAK,CAACoN,YAAY,GAAG,KAAK;IAC9B,CAAC,CAAC;EACN;EACA,IAAK,CAAErW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOqS,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAEvS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;IAC3K,MAAMqX,aAAa,GAAG;MAClBC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClB;MACAC,UAAU,EAAE;IAChB,CAAC;IACD,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC1K,OAAO,CAAE2K,CAAC,IAAK;MAClE/X,MAAM,CAAC8U,cAAc,CAACnM,KAAK,EAAEoP,CAAC,EAAEnS,MAAM,CAAC;QAAEmB,KAAK,EAAE4B,KAAK,CAACoP,CAAC;MAAE,CAAC,EAAEJ,aAAa,CAAC,CAAC;IAC/E,CAAC,CAAC;EACN;EACA;EACApY,KAAK,CAAC6R,EAAE,CAAChE,OAAO,CAAE4K,QAAQ,IAAK;IAC3B;IACA,IAAK,CAAEtY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAM,OAAOqS,qBAAqB,KAAK,WAAW,IAAIA,qBAAsB,KAAK,EAAEvS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,CAAC,IAAKU,SAAS,EAAE;MAC3K,MAAM2X,UAAU,GAAGxG,KAAK,CAACC,GAAG,CAAC,MAAMsG,QAAQ,CAAC;QACxCrP,KAAK,EAAEA,KAAK;QACZ8C,GAAG,EAAElM,KAAK,CAACsS,EAAE;QACbtS,KAAK;QACL4R,OAAO,EAAEsE;MACb,CAAC,CAAC,CAAC;MACHzV,MAAM,CAACoJ,IAAI,CAAC6O,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC7K,OAAO,CAAEjF,GAAG,IAAKQ,KAAK,CAACsB,iBAAiB,CAACwK,GAAG,CAACtM,GAAG,CAAC,CAAC;MAChFvC,MAAM,CAAC+C,KAAK,EAAEsP,UAAU,CAAC;IAC7B,CAAC,MACI;MACDrS,MAAM,CAAC+C,KAAK,EAAE8I,KAAK,CAACC,GAAG,CAAC,MAAMsG,QAAQ,CAAC;QACnCrP,KAAK,EAAEA,KAAK;QACZ8C,GAAG,EAAElM,KAAK,CAACsS,EAAE;QACbtS,KAAK;QACL4R,OAAO,EAAEsE;MACb,CAAC,CAAC,CAAC,CAAC;IACR;EACJ,CAAC,CAAC;EACF,IAAK/V,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACtC+I,KAAK,CAACmB,MAAM,IACZ,OAAOnB,KAAK,CAACmB,MAAM,KAAK,QAAQ,IAChC,OAAOnB,KAAK,CAACmB,MAAM,CAACoO,WAAW,KAAK,UAAU,IAC9C,CAACvP,KAAK,CAACmB,MAAM,CAACoO,WAAW,CAAChY,QAAQ,CAAC,CAAC,CAACsG,QAAQ,CAAC,eAAe,CAAC,EAAE;IAChEvE,OAAO,CAACiE,IAAI,CAAC,0DAA0D,GACnE,gCAAgC,GAChC,mBAAmByC,KAAK,CAACG,GAAG,IAAI,CAAC;EACzC;EACA;EACA,IAAIqM,YAAY,IACZK,cAAc,IACdrE,OAAO,CAACgH,OAAO,EAAE;IACjBhH,OAAO,CAACgH,OAAO,CAACxP,KAAK,CAACmB,MAAM,EAAEqL,YAAY,CAAC;EAC/C;EACAU,WAAW,GAAG,IAAI;EAClBG,eAAe,GAAG,IAAI;EACtB,OAAOrN,KAAK;AAChB;AACA;AACA;AACA,SAASyP,WAAWA;AACpB;AACAxP,EAAE,EAAEwM,KAAK,EAAEiD,YAAY,EAAE;EACrB,IAAIlH,OAAO;EACX,MAAMmH,YAAY,GAAG,OAAOlD,KAAK,KAAK,UAAU;EAChD;EACAjE,OAAO,GAAGmH,YAAY,GAAGD,YAAY,GAAGjD,KAAK;EAC7C,SAAS9B,QAAQA,CAAC/T,KAAK,EAAE2T,GAAG,EAAE;IAC1B,MAAMqF,UAAU,GAAGra,mBAAmB,CAAC,CAAC;IACxCqB,KAAK;IACD;IACA;IACA,CAAEG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAKP,WAAW,IAAIA,WAAW,CAACgS,QAAQ,GAAG,IAAI,GAAG9R,KAAK,MACnFgZ,UAAU,GAAGpa,MAAM,CAACsB,WAAW,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;IACvD,IAAIF,KAAK,EACLD,cAAc,CAACC,KAAK,CAAC;IACzB,IAAKG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAK,CAACP,WAAW,EAAE;MACzD,MAAM,IAAIqG,KAAK,CAAC,qIAAqI,GACjJ,oFAAoF,GACpF,+BAA+B,CAAC;IACxC;IACAnG,KAAK,GAAGF,WAAW;IACnB,IAAI,CAACE,KAAK,CAAC4J,EAAE,CAAC+E,GAAG,CAACtF,EAAE,CAAC,EAAE;MACnB;MACA,IAAI0P,YAAY,EAAE;QACd/C,gBAAgB,CAAC3M,EAAE,EAAEwM,KAAK,EAAEjE,OAAO,EAAE5R,KAAK,CAAC;MAC/C,CAAC,MACI;QACD2V,kBAAkB,CAACtM,EAAE,EAAEuI,OAAO,EAAE5R,KAAK,CAAC;MAC1C;MACA;MACA,IAAKG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAG;QACzC;QACA0T,QAAQ,CAACF,MAAM,GAAG7T,KAAK;MAC3B;IACJ;IACA,MAAMoJ,KAAK,GAAGpJ,KAAK,CAAC4J,EAAE,CAACQ,GAAG,CAACf,EAAE,CAAC;IAC9B,IAAKlJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKsT,GAAG,EAAE;MAChD,MAAMsF,KAAK,GAAG,QAAQ,GAAG5P,EAAE;MAC3B,MAAMsH,QAAQ,GAAGoI,YAAY,GACvB/C,gBAAgB,CAACiD,KAAK,EAAEpD,KAAK,EAAEjE,OAAO,EAAE5R,KAAK,EAAE,IAAI,CAAC,GACpD2V,kBAAkB,CAACsD,KAAK,EAAE5S,MAAM,CAAC,CAAC,CAAC,EAAEuL,OAAO,CAAC,EAAE5R,KAAK,EAAE,IAAI,CAAC;MACjE2T,GAAG,CAACjD,UAAU,CAACC,QAAQ,CAAC;MACxB;MACA,OAAO3Q,KAAK,CAACuH,KAAK,CAACC,KAAK,CAACyR,KAAK,CAAC;MAC/BjZ,KAAK,CAAC4J,EAAE,CAACsN,MAAM,CAAC+B,KAAK,CAAC;IAC1B;IACA,IAAK9Y,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKU,SAAS,EAAE;MACtD,MAAMmY,eAAe,GAAG3Z,kBAAkB,CAAC,CAAC;MAC5C;MACA,IAAI2Z,eAAe,IACfA,eAAe,CAAC1L,KAAK;MACrB;MACA,CAACmG,GAAG,EAAE;QACN,MAAMwF,EAAE,GAAGD,eAAe,CAAC1L,KAAK;QAChC,MAAM4L,KAAK,GAAG,UAAU,IAAID,EAAE,GAAGA,EAAE,CAACzL,QAAQ,GAAIyL,EAAE,CAACzL,QAAQ,GAAG,CAAC,CAAE;QACjE0L,KAAK,CAAC/P,EAAE,CAAC,GAAGD,KAAK;MACrB;IACJ;IACA;IACA,OAAOA,KAAK;EAChB;EACA2K,QAAQ,CAACxK,GAAG,GAAGF,EAAE;EACjB,OAAO0K,QAAQ;AACnB;AAEA,IAAIsF,cAAc,GAAG,OAAO;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,MAAM,CAAC;AAAA,EAChC;EACEF,cAAc,GAAGE,MAAM;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAC,GAAGtL,MAAM,EAAE;EAC1B,IAAK/N,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAKqJ,KAAK,CAACqB,OAAO,CAACmD,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IACrExL,OAAO,CAACiE,IAAI,CAAC,qFAAqF,GAC9F,WAAW,GACX,6CAA6C,GAC7C,QAAQ,GACR,2CAA2C,GAC3C,4CAA4C,CAAC;IACjDuH,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;EACtB;EACA,OAAOA,MAAM,CAAC5D,MAAM,CAAC,CAACmP,OAAO,EAAE1F,QAAQ,KAAK;IACxC;IACA0F,OAAO,CAAC1F,QAAQ,CAACxK,GAAG,GAAG8P,cAAc,CAAC,GAAG,YAAY;MACjD,OAAOtF,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC;IAChC,CAAC;IACD,OAAOoL,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC3F,QAAQ,EAAE4F,YAAY,EAAE;EACtC,OAAOjQ,KAAK,CAACqB,OAAO,CAAC4O,YAAY,CAAC,GAC5BA,YAAY,CAACrP,MAAM,CAAC,CAACmP,OAAO,EAAE7Q,GAAG,KAAK;IACpC6Q,OAAO,CAAC7Q,GAAG,CAAC,GAAG,YAAY;MACvB;MACA,OAAOmL,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACzF,GAAG,CAAC;IACrC,CAAC;IACD,OAAO6Q,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,GACJhZ,MAAM,CAACoJ,IAAI,CAAC8P,YAAY,CAAC,CAACrP,MAAM,CAAC,CAACmP,OAAO,EAAE7Q,GAAG,KAAK;IACjD;IACA6Q,OAAO,CAAC7Q,GAAG,CAAC,GAAG,YAAY;MACvB,MAAMQ,KAAK,GAAG2K,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC;MACnC,MAAMuL,QAAQ,GAAGD,YAAY,CAAC/Q,GAAG,CAAC;MAClC;MACA;MACA,OAAO,OAAOgR,QAAQ,KAAK,UAAU,GAC/BA,QAAQ,CAAChZ,IAAI,CAAC,IAAI,EAAEwI,KAAK,CAAC;MAC1B;MACEA,KAAK,CAACwQ,QAAQ,CAAC;IAC3B,CAAC;IACD,OAAOH,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;AACA;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAGH,QAAQ;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,UAAUA,CAAC/F,QAAQ,EAAE4F,YAAY,EAAE;EACxC,OAAOjQ,KAAK,CAACqB,OAAO,CAAC4O,YAAY,CAAC,GAC5BA,YAAY,CAACrP,MAAM,CAAC,CAACmP,OAAO,EAAE7Q,GAAG,KAAK;IACpC;IACA6Q,OAAO,CAAC7Q,GAAG,CAAC,GAAG,UAAU,GAAG6G,IAAI,EAAE;MAC9B;MACA,OAAOsE,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACzF,GAAG,CAAC,CAAC,GAAG6G,IAAI,CAAC;IAC9C,CAAC;IACD,OAAOgK,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,GACJhZ,MAAM,CAACoJ,IAAI,CAAC8P,YAAY,CAAC,CAACrP,MAAM,CAAC,CAACmP,OAAO,EAAE7Q,GAAG,KAAK;IACjD;IACA6Q,OAAO,CAAC7Q,GAAG,CAAC,GAAG,UAAU,GAAG6G,IAAI,EAAE;MAC9B;MACA,OAAOsE,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACsL,YAAY,CAAC/Q,GAAG,CAAC,CAAC,CAAC,GAAG6G,IAAI,CAAC;IAC5D,CAAC;IACD,OAAOgK,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAAChG,QAAQ,EAAE4F,YAAY,EAAE;EAC9C,OAAOjQ,KAAK,CAACqB,OAAO,CAAC4O,YAAY,CAAC,GAC5BA,YAAY,CAACrP,MAAM,CAAC,CAACmP,OAAO,EAAE7Q,GAAG,KAAK;IACpC6Q,OAAO,CAAC7Q,GAAG,CAAC,GAAG;MACXwB,GAAGA,CAAA,EAAG;QACF,OAAO2J,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACzF,GAAG,CAAC;MACrC,CAAC;MACDiG,GAAGA,CAACrH,KAAK,EAAE;QACP,OAAQuM,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACzF,GAAG,CAAC,GAAGpB,KAAK;MAC9C;IACJ,CAAC;IACD,OAAOiS,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC,GACJhZ,MAAM,CAACoJ,IAAI,CAAC8P,YAAY,CAAC,CAACrP,MAAM,CAAC,CAACmP,OAAO,EAAE7Q,GAAG,KAAK;IACjD6Q,OAAO,CAAC7Q,GAAG,CAAC,GAAG;MACXwB,GAAGA,CAAA,EAAG;QACF,OAAO2J,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACsL,YAAY,CAAC/Q,GAAG,CAAC,CAAC;MACnD,CAAC;MACDiG,GAAGA,CAACrH,KAAK,EAAE;QACP,OAAQuM,QAAQ,CAAC,IAAI,CAAC1F,MAAM,CAAC,CAACsL,YAAY,CAAC/Q,GAAG,CAAC,CAAC,GAAGpB,KAAK;MAC5D;IACJ,CAAC;IACD,OAAOiS,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,WAAWA,CAAC5Q,KAAK,EAAE;EACxB,MAAM6Q,QAAQ,GAAGpb,KAAK,CAACuK,KAAK,CAAC;EAC7B,MAAM8Q,IAAI,GAAG,CAAC,CAAC;EACf,KAAK,MAAMtR,GAAG,IAAIqR,QAAQ,EAAE;IACxB,MAAMzS,KAAK,GAAGyS,QAAQ,CAACrR,GAAG,CAAC;IAC3B;IACA;IACA,IAAIpB,KAAK,CAACkO,MAAM,EAAE;MACd;MACAwE,IAAI,CAACtR,GAAG,CAAC;MACL;MACAjJ,QAAQ,CAAC;QACLyK,GAAG,EAAEA,CAAA,KAAMhB,KAAK,CAACR,GAAG,CAAC;QACrBiG,GAAGA,CAACrH,KAAK,EAAE;UACP4B,KAAK,CAACR,GAAG,CAAC,GAAGpB,KAAK;QACtB;MACJ,CAAC,CAAC;IACV,CAAC,MACI,IAAIrI,KAAK,CAACqI,KAAK,CAAC,IAAIpI,UAAU,CAACoI,KAAK,CAAC,EAAE;MACxC;MACA0S,IAAI,CAACtR,GAAG,CAAC;MACL;MACAnJ,KAAK,CAAC2J,KAAK,EAAER,GAAG,CAAC;IACzB;EACJ;EACA,OAAOsR,IAAI;AACf;AAEA,SAASpZ,YAAY,EAAE2S,eAAe,EAAExB,WAAW,EAAE4G,WAAW,EAAE9F,YAAY,EAAE9S,cAAc,EAAE6Z,UAAU,EAAED,UAAU,EAAEH,QAAQ,EAAEF,SAAS,EAAEO,gBAAgB,EAAEha,cAAc,EAAEuZ,iBAAiB,EAAE9D,aAAa,EAAEH,WAAW,EAAE2E,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}