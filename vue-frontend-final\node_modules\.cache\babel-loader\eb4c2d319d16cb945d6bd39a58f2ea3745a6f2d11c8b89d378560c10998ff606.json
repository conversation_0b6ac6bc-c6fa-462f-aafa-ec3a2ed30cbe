{"ast": null, "code": "// src/index.ts\nimport { addCustomCommand, addCustomTab, onDevToolsClientConnected, onDevToolsConnected, removeCustomCommand, setupDevToolsPlugin, setupDevToolsPlugin as setupDevToolsPlugin2 } from \"@vue/devtools-kit\";\nexport { addCustomCommand, addCustomTab, onDevToolsClientConnected, onDevToolsConnected, removeCustomCommand, setupDevToolsPlugin, setupDevToolsPlugin2 as setupDevtoolsPlugin };", "map": {"version": 3, "names": ["addCustomCommand", "addCustomTab", "onDevToolsClientConnected", "onDevToolsConnected", "removeCustomCommand", "setupDevToolsPlugin", "setupDevToolsPlugin2", "setupDevtoolsPlugin"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/new/vue-frontend-project/vue-frontend-final/node_modules/@vue/devtools-api/dist/index.js"], "sourcesContent": ["// src/index.ts\nimport {\n  addCustomCommand,\n  addCustomTab,\n  onDevToolsClientConnected,\n  onDevToolsConnected,\n  removeCustomCommand,\n  setupDevToolsPlugin,\n  setupDevToolsPlugin as setupDevToolsPlugin2\n} from \"@vue/devtools-kit\";\nexport {\n  addCustomCommand,\n  addCustomTab,\n  onDevToolsClientConnected,\n  onDevToolsConnected,\n  removeCustomCommand,\n  setupDevToolsPlugin,\n  setupDevToolsPlugin2 as setupDevtoolsPlugin\n};\n"], "mappings": "AAAA;AACA,SACEA,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,EACnBA,mBAAmB,IAAIC,oBAAoB,QACtC,mBAAmB;AAC1B,SACEN,gBAAgB,EAChBC,YAAY,EACZC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,IAAIC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}