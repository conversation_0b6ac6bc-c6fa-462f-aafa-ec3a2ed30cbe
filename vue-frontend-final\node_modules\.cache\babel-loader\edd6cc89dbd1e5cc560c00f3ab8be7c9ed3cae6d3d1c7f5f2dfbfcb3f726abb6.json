{"ast": null, "code": "import { createRouter, createWebHistory } from 'vue-router';\nimport LoginView from '../views/LoginView.vue';\nimport DashboardView from '../views/DashboardView.vue';\nconst routes = [{\n  path: '/',\n  redirect: '/login'\n}, {\n  path: '/login',\n  name: '<PERSON><PERSON>',\n  component: LoginView\n}, {\n  path: '/dashboard',\n  name: 'Dashboard',\n  component: DashboardView,\n  meta: {\n    requiresAuth: true\n  }\n}];\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n});\n\n// Navigation guard للتحقق من المصادقة\nrouter.beforeEach((to, from, next) => {\n  const isAuthenticated = localStorage.getItem('authToken');\n  if (to.meta.requiresAuth && !isAuthenticated) {\n    next('/login');\n  } else if (to.path === '/login' && isAuthenticated) {\n    next('/dashboard');\n  } else {\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "<PERSON><PERSON><PERSON>ie<PERSON>", "DashboardView", "routes", "path", "redirect", "name", "component", "meta", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "isAuthenticated", "localStorage", "getItem"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/new/vue-frontend-project/vue-frontend-final/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\nimport LoginView from '../views/LoginView.vue'\nimport DashboardView from '../views/DashboardView.vue'\n\nconst routes = [\n  {\n    path: '/',\n    redirect: '/login'\n  },\n  {\n    path: '/login',\n    name: '<PERSON><PERSON>',\n    component: LoginView\n  },\n  {\n    path: '/dashboard',\n    name: 'Dashboard',\n    component: DashboardView,\n    meta: { requiresAuth: true }\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes\n})\n\n// Navigation guard للتحقق من المصادقة\nrouter.beforeEach((to, from, next) => {\n  const isAuthenticated = localStorage.getItem('authToken')\n  \n  if (to.meta.requiresAuth && !isAuthenticated) {\n    next('/login')\n  } else if (to.path === '/login' && isAuthenticated) {\n    next('/dashboard')\n  } else {\n    next()\n  }\n})\n\nexport default router\n\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AAEtD,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEN;AACb,CAAC,EACD;EACEG,IAAI,EAAE,YAAY;EAClBE,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEL,aAAa;EACxBM,IAAI,EAAE;IAAEC,YAAY,EAAE;EAAK;AAC7B,CAAC,CACF;AAED,MAAMC,MAAM,GAAGX,YAAY,CAAC;EAC1BY,OAAO,EAAEX,gBAAgB,CAACY,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CX;AACF,CAAC,CAAC;;AAEF;AACAO,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;EAEzD,IAAIL,EAAE,CAACR,IAAI,CAACC,YAAY,IAAI,CAACU,eAAe,EAAE;IAC5CD,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC,MAAM,IAAIF,EAAE,CAACZ,IAAI,KAAK,QAAQ,IAAIe,eAAe,EAAE;IAClDD,IAAI,CAAC,YAAY,CAAC;EACpB,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}