{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"main-content\"\n};\nconst _hoisted_3 = {\n  class: \"main-header\"\n};\nconst _hoisted_4 = {\n  class: \"header-left\"\n};\nconst _hoisted_5 = {\n  class: \"header-subtitle\"\n};\nconst _hoisted_6 = {\n  class: \"header-right\"\n};\nconst _hoisted_7 = {\n  class: \"user-info\"\n};\nconst _hoisted_8 = {\n  class: \"user-name\"\n};\nconst _hoisted_9 = {\n  class: \"content-area\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"dashboard-overview\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Sidebar = _resolveComponent(\"Sidebar\");\n  const _component_DashboardStats = _resolveComponent(\"DashboardStats\");\n  const _component_RecentActivities = _resolveComponent(\"RecentActivities\");\n  const _component_SectionPlaceholder = _resolveComponent(\"SectionPlaceholder\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" Sidebar \"), _createVNode(_component_Sidebar, {\n    collapsed: $setup.sidebarCollapsed,\n    \"active-section\": $setup.activeSection,\n    onToggle: $setup.toggleSidebar,\n    onSectionChange: $setup.setActiveSection,\n    onLogout: $setup.handleLogout\n  }, null, 8 /* PROPS */, [\"collapsed\", \"active-section\", \"onToggle\", \"onSectionChange\", \"onLogout\"]), _createCommentVNode(\" Main Content \"), _createElementVNode(\"main\", _hoisted_2, [_createCommentVNode(\" Header \"), _createElementVNode(\"header\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h1\", null, _toDisplayString($setup.getSectionTitle()), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_5, _toDisplayString($setup.getSectionSubtitle()), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"span\", _hoisted_8, _toDisplayString($setup.authStore.currentUser?.fullName), 1 /* TEXT */), _cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"user-avatar\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user\"\n  })], -1 /* CACHED */))])])]), _createCommentVNode(\" Content Area \"), _createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" Dashboard Overview \"), $setup.activeSection === 'dashboard' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_DashboardStats, {\n    stats: $setup.dashboardStatsArray\n  }, null, 8 /* PROPS */, [\"stats\"]), _createVNode(_component_RecentActivities, {\n    activities: $setup.recentActivities\n  }, null, 8 /* PROPS */, [\"activities\"])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Other Sections \"), _createVNode(_component_SectionPlaceholder, {\n    icon: $setup.getSectionIcon(),\n    title: $setup.getSectionTitle()\n  }, null, 8 /* PROPS */, [\"icon\", \"title\"])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_Sidebar", "collapsed", "$setup", "sidebarCollapsed", "activeSection", "onToggle", "toggleSidebar", "onSectionChange", "setActiveSection", "onLogout", "handleLogout", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "getSectionTitle", "_hoisted_5", "getSectionSubtitle", "_hoisted_6", "_hoisted_7", "_hoisted_8", "authStore", "currentUser", "fullName", "_hoisted_9", "_hoisted_10", "_component_DashboardStats", "stats", "dashboardStatsArray", "_component_RecentActivities", "activities", "recentActivities", "_Fragment", "key", "_component_SectionPlaceholder", "icon", "getSectionIcon", "title"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\views\\DashboardView.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- Sidebar -->\n    <Sidebar \n      :collapsed=\"sidebarCollapsed\"\n      :active-section=\"activeSection\"\n      @toggle=\"toggleSidebar\"\n      @section-change=\"setActiveSection\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Main Content -->\n    <main class=\"main-content\">\n      <!-- Header -->\n      <header class=\"main-header\">\n        <div class=\"header-left\">\n          <h1>{{ getSectionTitle() }}</h1>\n          <p class=\"header-subtitle\">{{ getSectionSubtitle() }}</p>\n        </div>\n        <div class=\"header-right\">\n          <div class=\"user-info\">\n            <span class=\"user-name\">{{ authStore.currentUser?.fullName }}</span>\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <!-- Content Area -->\n      <div class=\"content-area\">\n        <!-- Dashboard Overview -->\n        <div v-if=\"activeSection === 'dashboard'\" class=\"dashboard-overview\">\n          <DashboardStats :stats=\"dashboardStatsArray\" />\n          <RecentActivities :activities=\"recentActivities\" />\n        </div>\n\n        <!-- Other Sections -->\n        <SectionPlaceholder \n          v-else\n          :icon=\"getSectionIcon()\"\n          :title=\"getSectionTitle()\"\n        />\n      </div>\n    </main>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '../stores/auth'\nimport Sidebar from '../components/Sidebar.vue'\nimport DashboardStats from '../components/DashboardStats.vue'\nimport RecentActivities from '../components/RecentActivities.vue'\nimport SectionPlaceholder from '../components/SectionPlaceholder.vue'\n\nexport default {\n  name: 'DashboardView',\n  components: {\n    Sidebar,\n    DashboardStats,\n    RecentActivities,\n    SectionPlaceholder\n  },\n  setup() {\n    const router = useRouter()\n    const authStore = useAuthStore()\n    \n    // Reactive data\n    const sidebarCollapsed = ref(false)\n    const activeSection = ref('dashboard')\n    \n    const dashboardStats = reactive({\n      totalProducts: 1247,\n      totalSales: 125000,\n      activeCustomers: 892,\n      availableStock: 3456\n    })\n\n    const dashboardStatsArray = computed(() => [\n      {\n        id: 'products',\n        title: 'إجمالي المنتجات',\n        value: dashboardStats.totalProducts,\n        type: 'number',\n        icon: 'fas fa-box text-blue-500',\n        change: 12\n      },\n      {\n        id: 'sales',\n        title: 'إجمالي المبيعات',\n        value: dashboardStats.totalSales,\n        type: 'currency',\n        icon: 'fas fa-chart-line text-green-500',\n        change: 8\n      },\n      {\n        id: 'customers',\n        title: 'العملاء النشطين',\n        value: dashboardStats.activeCustomers,\n        type: 'number',\n        icon: 'fas fa-users text-purple-500',\n        change: 15\n      },\n      {\n        id: 'stock',\n        title: 'المخزون المتاح',\n        value: dashboardStats.availableStock,\n        type: 'number',\n        icon: 'fas fa-warehouse text-orange-500',\n        change: -3\n      }\n    ])\n\n    const recentActivities = ref([\n      {\n        id: 1,\n        icon: 'fas fa-plus-circle text-green-500',\n        text: 'تم إضافة منتج جديد: iPhone 15 Pro',\n        time: 'منذ 5 دقائق'\n      },\n      {\n        id: 2,\n        icon: 'fas fa-shopping-cart text-blue-500',\n        text: 'طلب جديد من العميل أحمد محمد',\n        time: 'منذ 15 دقيقة'\n      },\n      {\n        id: 3,\n        icon: 'fas fa-user-plus text-purple-500',\n        text: 'تم تسجيل عميل جديد: سارة أحمد',\n        time: 'منذ 30 دقيقة'\n      },\n      {\n        id: 4,\n        icon: 'fas fa-truck text-orange-500',\n        text: 'تم استلام شحنة من المورد الرئيسي',\n        time: 'منذ ساعة'\n      }\n    ])\n\n    // Methods\n    const toggleSidebar = () => {\n      sidebarCollapsed.value = !sidebarCollapsed.value\n    }\n\n    const setActiveSection = (section) => {\n      activeSection.value = section\n    }\n\n    const getSectionTitle = () => {\n      const titles = {\n        dashboard: 'لوحة التحكم',\n        products: 'إدارة المنتجات',\n        inventory: 'إدارة المخزون',\n        sales: 'تقارير المبيعات',\n        users: 'إدارة المستخدمين',\n        suppliers: 'إدارة الموردين'\n      }\n      return titles[activeSection.value] || 'لوحة التحكم'\n    }\n\n    const getSectionSubtitle = () => {\n      const subtitles = {\n        dashboard: 'نظرة عامة على أداء المتجر',\n        products: 'إضافة وتعديل وحذف المنتجات',\n        inventory: 'متابعة المخزون والكميات',\n        sales: 'تحليل المبيعات والأرباح',\n        users: 'إدارة حسابات المستخدمين',\n        suppliers: 'إدارة بيانات الموردين'\n      }\n      return subtitles[activeSection.value] || ''\n    }\n\n    const getSectionIcon = () => {\n      const icons = {\n        dashboard: 'fas fa-tachometer-alt',\n        products: 'fas fa-box',\n        inventory: 'fas fa-warehouse',\n        sales: 'fas fa-chart-line',\n        users: 'fas fa-users',\n        suppliers: 'fas fa-truck'\n      }\n      return icons[activeSection.value] || 'fas fa-tachometer-alt'\n    }\n\n    const handleLogout = () => {\n      authStore.logout()\n      router.push('/login')\n    }\n\n    // Lifecycle\n    onMounted(() => {\n      authStore.initializeAuth()\n    })\n\n    return {\n      sidebarCollapsed,\n      activeSection,\n      dashboardStatsArray,\n      recentActivities,\n      authStore,\n      toggleSidebar,\n      setActiveSection,\n      getSectionTitle,\n      getSectionSubtitle,\n      getSectionIcon,\n      handleLogout\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard-container {\n  display: flex;\n  min-height: 100vh;\n  background-color: #f8fafc;\n  font-family: 'Cairo', sans-serif;\n  direction: rtl;\n}\n\n/* ===== Main Content Styles ===== */\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.main-header {\n  background: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.header-left h1 {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n}\n\n.header-subtitle {\n  color: #64748b;\n  margin: 0;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.user-name {\n  font-weight: 500;\n  color: #334155;\n}\n\n.user-avatar {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n/* ===== Content Area Styles ===== */\n.content-area {\n  flex: 1;\n  padding: 2rem;\n  overflow-y: auto;\n}\n\n.dashboard-overview {\n  max-width: 1200px;\n}\n\n/* ===== Responsive Design ===== */\n@media (max-width: 768px) {\n  .main-header {\n    padding: 1rem;\n  }\n\n  .content-area {\n    padding: 1rem;\n  }\n}\n\n/* ===== Color Utilities ===== */\n.text-blue-500 { color: #3b82f6; }\n.text-green-500 { color: #10b981; }\n.text-purple-500 { color: #8b5cf6; }\n.text-orange-500 { color: #f59e0b; }\n</style>\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAWxBA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAa;;EACpBA,KAAK,EAAC;AAAa;;EAEnBA,KAAK,EAAC;AAAiB;;EAEvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;EASxBA,KAAK,EAAC;AAAc;;;EAEmBA,KAAK,EAAC;;;;;;;uBA/BtDC,mBAAA,CA4CM,OA5CNC,UA4CM,GA3CJC,mBAAA,aAAgB,EAChBC,YAAA,CAMEC,kBAAA;IALCC,SAAS,EAAEC,MAAA,CAAAC,gBAAgB;IAC3B,gBAAc,EAAED,MAAA,CAAAE,aAAa;IAC7BC,QAAM,EAAEH,MAAA,CAAAI,aAAa;IACrBC,eAAc,EAAEL,MAAA,CAAAM,gBAAgB;IAChCC,QAAM,EAAEP,MAAA,CAAAQ;uGAGXZ,mBAAA,kBAAqB,EACrBa,mBAAA,CAgCO,QAhCPC,UAgCO,GA/BLd,mBAAA,YAAe,EACfa,mBAAA,CAaS,UAbTE,UAaS,GAZPF,mBAAA,CAGM,OAHNG,UAGM,GAFJH,mBAAA,CAAgC,YAAAI,gBAAA,CAAzBb,MAAA,CAAAc,eAAe,oBACtBL,mBAAA,CAAyD,KAAzDM,UAAyD,EAAAF,gBAAA,CAA3Bb,MAAA,CAAAgB,kBAAkB,mB,GAElDP,mBAAA,CAOM,OAPNQ,UAOM,GANJR,mBAAA,CAKM,OALNS,UAKM,GAJJT,mBAAA,CAAoE,QAApEU,UAAoE,EAAAN,gBAAA,CAAzCb,MAAA,CAAAoB,SAAS,CAACC,WAAW,EAAEC,QAAQ,kB,0BAC1Db,mBAAA,CAEM;IAFDhB,KAAK,EAAC;EAAa,IACtBgB,mBAAA,CAA2B;IAAxBhB,KAAK,EAAC;EAAa,G,2BAM9BG,mBAAA,kBAAqB,EACrBa,mBAAA,CAaM,OAbNc,UAaM,GAZJ3B,mBAAA,wBAA2B,EAChBI,MAAA,CAAAE,aAAa,oB,cAAxBR,mBAAA,CAGM,OAHN8B,WAGM,GAFJ3B,YAAA,CAA+C4B,yBAAA;IAA9BC,KAAK,EAAE1B,MAAA,CAAA2B;EAAmB,oCAC3C9B,YAAA,CAAmD+B,2BAAA;IAAhCC,UAAU,EAAE7B,MAAA,CAAA8B;EAAgB,wC,oBAIjDpC,mBAAA,CAIEqC,SAAA;IAAAC,GAAA;EAAA,IALFpC,mBAAA,oBAAuB,EACvBC,YAAA,CAIEoC,6BAAA;IAFCC,IAAI,EAAElC,MAAA,CAAAmC,cAAc;IACpBC,KAAK,EAAEpC,MAAA,CAAAc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}