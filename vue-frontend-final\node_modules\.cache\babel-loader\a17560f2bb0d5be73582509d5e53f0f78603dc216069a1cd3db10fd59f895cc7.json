{"ast": null, "code": "export default {\n  name: 'RecentActivities',\n  props: {\n    activities: {\n      type: Array,\n      required: true\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "activities", "type", "Array", "required"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\RecentActivities.vue"], "sourcesContent": ["<template>\n  <div class=\"recent-activities\">\n    <h2>الأنشطة الأخيرة</h2>\n    <div class=\"activities-list\">\n      <div v-for=\"activity in activities\" :key=\"activity.id\" class=\"activity-item\">\n        <div class=\"activity-icon\">\n          <i :class=\"activity.icon\"></i>\n        </div>\n        <div class=\"activity-content\">\n          <p class=\"activity-text\">{{ activity.text }}</p>\n          <span class=\"activity-time\">{{ activity.time }}</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'RecentActivities',\n  props: {\n    activities: {\n      type: Array,\n      required: true\n    }\n  }\n}\n</script>\n\n<style scoped>\n.recent-activities {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.recent-activities h2 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\n\n.activities-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.activity-item {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background: #f8fafc;\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.activity-item:hover {\n  background: #f1f5f9;\n  transform: translateX(-2px);\n}\n\n.activity-icon {\n  width: 40px;\n  height: 40px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.activity-content {\n  flex: 1;\n}\n\n.activity-text {\n  font-weight: 500;\n  color: #334155;\n  margin: 0 0 4px 0;\n}\n\n.activity-time {\n  font-size: 0.75rem;\n  color: #64748b;\n}\n</style>\n\n"], "mappings": "AAkBA,eAAe;EACbA,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,KAAK;MACXC,QAAQ,EAAE;IACZ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}