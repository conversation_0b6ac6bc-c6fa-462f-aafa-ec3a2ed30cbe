{"ast": null, "code": "let supported;\nlet perf;\nexport function isPerformanceSupported() {\n  var _a;\n  if (supported !== undefined) {\n    return supported;\n  }\n  if (typeof window !== 'undefined' && window.performance) {\n    supported = true;\n    perf = window.performance;\n  } else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n    supported = true;\n    perf = globalThis.perf_hooks.performance;\n  } else {\n    supported = false;\n  }\n  return supported;\n}\nexport function now() {\n  return isPerformanceSupported() ? perf.now() : Date.now();\n}", "map": {"version": 3, "names": ["supported", "perf", "isPerformanceSupported", "_a", "undefined", "window", "performance", "globalThis", "perf_hooks", "now", "Date"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/new/vue-frontend-project/vue-frontend-final/node_modules/vue-router/node_modules/@vue/devtools-api/lib/esm/time.js"], "sourcesContent": ["let supported;\nlet perf;\nexport function isPerformanceSupported() {\n    var _a;\n    if (supported !== undefined) {\n        return supported;\n    }\n    if (typeof window !== 'undefined' && window.performance) {\n        supported = true;\n        perf = window.performance;\n    }\n    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n        supported = true;\n        perf = globalThis.perf_hooks.performance;\n    }\n    else {\n        supported = false;\n    }\n    return supported;\n}\nexport function now() {\n    return isPerformanceSupported() ? perf.now() : Date.now();\n}\n"], "mappings": "AAAA,IAAIA,SAAS;AACb,IAAIC,IAAI;AACR,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACrC,IAAIC,EAAE;EACN,IAAIH,SAAS,KAAKI,SAAS,EAAE;IACzB,OAAOJ,SAAS;EACpB;EACA,IAAI,OAAOK,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,WAAW,EAAE;IACrDN,SAAS,GAAG,IAAI;IAChBC,IAAI,GAAGI,MAAM,CAACC,WAAW;EAC7B,CAAC,MACI,IAAI,OAAOC,UAAU,KAAK,WAAW,KAAK,CAACJ,EAAE,GAAGI,UAAU,CAACC,UAAU,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,WAAW,CAAC,EAAE;IAC9HN,SAAS,GAAG,IAAI;IAChBC,IAAI,GAAGM,UAAU,CAACC,UAAU,CAACF,WAAW;EAC5C,CAAC,MACI;IACDN,SAAS,GAAG,KAAK;EACrB;EACA,OAAOA,SAAS;AACpB;AACA,OAAO,SAASS,GAAGA,CAAA,EAAG;EAClB,OAAOP,sBAAsB,CAAC,CAAC,GAAGD,IAAI,CAACQ,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}