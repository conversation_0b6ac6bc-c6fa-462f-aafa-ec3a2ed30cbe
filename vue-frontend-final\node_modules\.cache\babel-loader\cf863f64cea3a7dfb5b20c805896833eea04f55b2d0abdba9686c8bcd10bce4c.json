{"ast": null, "code": "export default {\n  name: 'AppSidebar',\n  props: {\n    collapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeSection: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n  emits: ['toggle', 'section-change', 'logout'],\n  data() {\n    return {\n      menuItems: [{\n        id: 'dashboard',\n        title: 'لوحة التحكم',\n        icon: 'fas fa-tachometer-alt'\n      }, {\n        id: 'products',\n        title: 'المنتجات',\n        icon: 'fas fa-box'\n      }, {\n        id: 'inventory',\n        title: 'المخزون',\n        icon: 'fas fa-warehouse'\n      }, {\n        id: 'sales',\n        title: 'المبيعات',\n        icon: 'fas fa-chart-line'\n      }, {\n        id: 'users',\n        title: 'المستخدمين',\n        icon: 'fas fa-users'\n      }, {\n        id: 'suppliers',\n        title: 'الموردين',\n        icon: 'fas fa-truck'\n      }]\n    };\n  }\n};", "map": {"version": 3, "names": ["name", "props", "collapsed", "type", "Boolean", "default", "activeSection", "String", "emits", "data", "menuItems", "id", "title", "icon"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\Sidebar.vue"], "sourcesContent": ["<template>\n  <aside class=\"sidebar\" :class=\"{ 'sidebar-collapsed': collapsed }\">\n    <div class=\"sidebar-header\">\n      <div class=\"logo\">\n        <i class=\"fas fa-store\"></i>\n        <span v-if=\"!collapsed\">ElectroHub Pro</span>\n      </div>\n      <button @click=\"$emit('toggle')\" class=\"sidebar-toggle\">\n        <i :class=\"collapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'\"></i>\n      </button>\n    </div>\n\n    <nav class=\"sidebar-nav\">\n      <ul>\n        <li v-for=\"item in menuItems\" :key=\"item.id\">\n          <a href=\"#\" @click=\"$emit('section-change', item.id)\" \n             :class=\"{ active: activeSection === item.id }\" class=\"sidebar-item\">\n            <i :class=\"item.icon\"></i>\n            <span v-if=\"!collapsed\">{{ item.title }}</span>\n          </a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"sidebar-footer\">\n      <button @click=\"$emit('logout')\" class=\"logout-btn\">\n        <i class=\"fas fa-sign-out-alt\"></i>\n        <span v-if=\"!collapsed\">تسجيل الخروج</span>\n      </button>\n    </div>\n  </aside>\n</template>\n\n<script>\nexport default {\n  name: 'AppSidebar',\n  props: {\n    collapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeSection: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n  emits: ['toggle', 'section-change', 'logout'],\n  data() {\n    return {\n      menuItems: [\n        {\n          id: 'dashboard',\n          title: 'لوحة التحكم',\n          icon: 'fas fa-tachometer-alt'\n        },\n        {\n          id: 'products',\n          title: 'المنتجات',\n          icon: 'fas fa-box'\n        },\n        {\n          id: 'inventory',\n          title: 'المخزون',\n          icon: 'fas fa-warehouse'\n        },\n        {\n          id: 'sales',\n          title: 'المبيعات',\n          icon: 'fas fa-chart-line'\n        },\n        {\n          id: 'users',\n          title: 'المستخدمين',\n          icon: 'fas fa-users'\n        },\n        {\n          id: 'suppliers',\n          title: 'الموردين',\n          icon: 'fas fa-truck'\n        }\n      ]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.sidebar {\n  width: 280px;\n  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n  color: white;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n}\n\n.sidebar-collapsed {\n  width: 80px;\n}\n\n.sidebar-header {\n  padding: 1.5rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 1.25rem;\n  font-weight: 700;\n}\n\n.logo i {\n  font-size: 1.5rem;\n  color: #0ea5e9;\n}\n\n.sidebar-toggle {\n  background: none;\n  border: none;\n  color: white;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: background-color 0.3s ease;\n}\n\n.sidebar-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-nav {\n  flex: 1;\n  padding: 1rem 0;\n}\n\n.sidebar-nav ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.sidebar-nav li {\n  margin-bottom: 4px;\n}\n\n.sidebar-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 1.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.sidebar-item:hover {\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  transform: translateX(-2px);\n  color: white;\n}\n\n.sidebar-item.active {\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  color: white;\n}\n\n.sidebar-item i {\n  width: 20px;\n  text-align: center;\n}\n\n.sidebar-footer {\n  padding: 1.5rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.logout-btn {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  width: 100%;\n  padding: 12px;\n  background: rgba(239, 68, 68, 0.1);\n  border: 1px solid rgba(239, 68, 68, 0.3);\n  color: #ef4444;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.logout-btn:hover {\n  background: rgba(239, 68, 68, 0.2);\n  transform: translateY(-1px);\n}\n</style>\n\n"], "mappings": "AAkCA,eAAe;EACbA,IAAI,EAAE,YAAY;EAClBC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,OAAO;MACbC,OAAO,EAAE;IACX,CAAC;IACDC,aAAa,EAAE;MACbH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDG,KAAK,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,CAAC;EAC7CC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,CACT;QACEC,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,UAAU;QACdC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,OAAO;QACXC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,OAAO;QACXC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE;MACR,CAAC,EACD;QACEF,EAAE,EAAE,WAAW;QACfC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE;MACR;IAEJ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}