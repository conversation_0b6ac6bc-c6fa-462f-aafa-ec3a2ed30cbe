{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useAuthStore } from '../stores/auth';\nimport Sidebar from '../components/Sidebar.vue';\nimport DashboardStats from '../components/DashboardStats.vue';\nimport RecentActivities from '../components/RecentActivities.vue';\nimport SectionPlaceholder from '../components/SectionPlaceholder.vue';\nexport default {\n  name: 'DashboardView',\n  components: {\n    Sidebar,\n    DashboardStats,\n    RecentActivities,\n    SectionPlaceholder\n  },\n  setup() {\n    const router = useRouter();\n    const authStore = useAuthStore();\n\n    // Reactive data\n    const sidebarCollapsed = ref(false);\n    const activeSection = ref('dashboard');\n    const dashboardStats = reactive({\n      totalProducts: 1247,\n      totalSales: 125000,\n      activeCustomers: 892,\n      availableStock: 3456\n    });\n    const dashboardStatsArray = computed(() => [{\n      id: 'products',\n      title: 'إجمالي المنتجات',\n      value: dashboardStats.totalProducts,\n      type: 'number',\n      icon: 'fas fa-box text-blue-500',\n      change: 12\n    }, {\n      id: 'sales',\n      title: 'إجمالي المبيعات',\n      value: dashboardStats.totalSales,\n      type: 'currency',\n      icon: 'fas fa-chart-line text-green-500',\n      change: 8\n    }, {\n      id: 'customers',\n      title: 'العملاء النشطين',\n      value: dashboardStats.activeCustomers,\n      type: 'number',\n      icon: 'fas fa-users text-purple-500',\n      change: 15\n    }, {\n      id: 'stock',\n      title: 'المخزون المتاح',\n      value: dashboardStats.availableStock,\n      type: 'number',\n      icon: 'fas fa-warehouse text-orange-500',\n      change: -3\n    }]);\n    const recentActivities = ref([{\n      id: 1,\n      icon: 'fas fa-plus-circle text-green-500',\n      text: 'تم إضافة منتج جديد: iPhone 15 Pro',\n      time: 'منذ 5 دقائق'\n    }, {\n      id: 2,\n      icon: 'fas fa-shopping-cart text-blue-500',\n      text: 'طلب جديد من العميل أحمد محمد',\n      time: 'منذ 15 دقيقة'\n    }, {\n      id: 3,\n      icon: 'fas fa-user-plus text-purple-500',\n      text: 'تم تسجيل عميل جديد: سارة أحمد',\n      time: 'منذ 30 دقيقة'\n    }, {\n      id: 4,\n      icon: 'fas fa-truck text-orange-500',\n      text: 'تم استلام شحنة من المورد الرئيسي',\n      time: 'منذ ساعة'\n    }]);\n\n    // Methods\n    const toggleSidebar = () => {\n      sidebarCollapsed.value = !sidebarCollapsed.value;\n    };\n    const setActiveSection = section => {\n      activeSection.value = section;\n    };\n    const getSectionTitle = () => {\n      const titles = {\n        dashboard: 'لوحة التحكم',\n        products: 'إدارة المنتجات',\n        inventory: 'إدارة المخزون',\n        sales: 'تقارير المبيعات',\n        users: 'إدارة المستخدمين',\n        suppliers: 'إدارة الموردين'\n      };\n      return titles[activeSection.value] || 'لوحة التحكم';\n    };\n    const getSectionSubtitle = () => {\n      const subtitles = {\n        dashboard: 'نظرة عامة على أداء المتجر',\n        products: 'إضافة وتعديل وحذف المنتجات',\n        inventory: 'متابعة المخزون والكميات',\n        sales: 'تحليل المبيعات والأرباح',\n        users: 'إدارة حسابات المستخدمين',\n        suppliers: 'إدارة بيانات الموردين'\n      };\n      return subtitles[activeSection.value] || '';\n    };\n    const getSectionIcon = () => {\n      const icons = {\n        dashboard: 'fas fa-tachometer-alt',\n        products: 'fas fa-box',\n        inventory: 'fas fa-warehouse',\n        sales: 'fas fa-chart-line',\n        users: 'fas fa-users',\n        suppliers: 'fas fa-truck'\n      };\n      return icons[activeSection.value] || 'fas fa-tachometer-alt';\n    };\n    const handleLogout = () => {\n      authStore.logout();\n      router.push('/login');\n    };\n\n    // Lifecycle\n    onMounted(() => {\n      authStore.initializeAuth();\n    });\n    return {\n      sidebarCollapsed,\n      activeSection,\n      dashboardStatsArray,\n      recentActivities,\n      authStore,\n      toggleSidebar,\n      setActiveSection,\n      getSectionTitle,\n      getSectionSubtitle,\n      getSectionIcon,\n      handleLogout\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRouter", "useAuthStore", "Sidebar", "DashboardStats", "RecentActivities", "SectionPlaceholder", "name", "components", "setup", "router", "authStore", "sidebarCollapsed", "activeSection", "dashboardStats", "totalProducts", "totalSales", "activeCustomers", "availableStock", "dashboardStatsArray", "id", "title", "value", "type", "icon", "change", "recentActivities", "text", "time", "toggleSidebar", "setActiveSection", "section", "getSectionTitle", "titles", "dashboard", "products", "inventory", "sales", "users", "suppliers", "getSectionSubtitle", "subtitles", "getSectionIcon", "icons", "handleLogout", "logout", "push", "initializeAuth"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\views\\DashboardView.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- Sidebar -->\n    <Sidebar \n      :collapsed=\"sidebarCollapsed\"\n      :active-section=\"activeSection\"\n      @toggle=\"toggleSidebar\"\n      @section-change=\"setActiveSection\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Main Content -->\n    <main class=\"main-content\">\n      <!-- Header -->\n      <header class=\"main-header\">\n        <div class=\"header-left\">\n          <h1>{{ getSectionTitle() }}</h1>\n          <p class=\"header-subtitle\">{{ getSectionSubtitle() }}</p>\n        </div>\n        <div class=\"header-right\">\n          <div class=\"user-info\">\n            <span class=\"user-name\">{{ authStore.currentUser?.fullName }}</span>\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user\"></i>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <!-- Content Area -->\n      <div class=\"content-area\">\n        <!-- Dashboard Overview -->\n        <div v-if=\"activeSection === 'dashboard'\" class=\"dashboard-overview\">\n          <DashboardStats :stats=\"dashboardStatsArray\" />\n          <RecentActivities :activities=\"recentActivities\" />\n        </div>\n\n        <!-- Other Sections -->\n        <SectionPlaceholder \n          v-else\n          :icon=\"getSectionIcon()\"\n          :title=\"getSectionTitle()\"\n        />\n      </div>\n    </main>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '../stores/auth'\nimport Sidebar from '../components/Sidebar.vue'\nimport DashboardStats from '../components/DashboardStats.vue'\nimport RecentActivities from '../components/RecentActivities.vue'\nimport SectionPlaceholder from '../components/SectionPlaceholder.vue'\n\nexport default {\n  name: 'DashboardView',\n  components: {\n    Sidebar,\n    DashboardStats,\n    RecentActivities,\n    SectionPlaceholder\n  },\n  setup() {\n    const router = useRouter()\n    const authStore = useAuthStore()\n    \n    // Reactive data\n    const sidebarCollapsed = ref(false)\n    const activeSection = ref('dashboard')\n    \n    const dashboardStats = reactive({\n      totalProducts: 1247,\n      totalSales: 125000,\n      activeCustomers: 892,\n      availableStock: 3456\n    })\n\n    const dashboardStatsArray = computed(() => [\n      {\n        id: 'products',\n        title: 'إجمالي المنتجات',\n        value: dashboardStats.totalProducts,\n        type: 'number',\n        icon: 'fas fa-box text-blue-500',\n        change: 12\n      },\n      {\n        id: 'sales',\n        title: 'إجمالي المبيعات',\n        value: dashboardStats.totalSales,\n        type: 'currency',\n        icon: 'fas fa-chart-line text-green-500',\n        change: 8\n      },\n      {\n        id: 'customers',\n        title: 'العملاء النشطين',\n        value: dashboardStats.activeCustomers,\n        type: 'number',\n        icon: 'fas fa-users text-purple-500',\n        change: 15\n      },\n      {\n        id: 'stock',\n        title: 'المخزون المتاح',\n        value: dashboardStats.availableStock,\n        type: 'number',\n        icon: 'fas fa-warehouse text-orange-500',\n        change: -3\n      }\n    ])\n\n    const recentActivities = ref([\n      {\n        id: 1,\n        icon: 'fas fa-plus-circle text-green-500',\n        text: 'تم إضافة منتج جديد: iPhone 15 Pro',\n        time: 'منذ 5 دقائق'\n      },\n      {\n        id: 2,\n        icon: 'fas fa-shopping-cart text-blue-500',\n        text: 'طلب جديد من العميل أحمد محمد',\n        time: 'منذ 15 دقيقة'\n      },\n      {\n        id: 3,\n        icon: 'fas fa-user-plus text-purple-500',\n        text: 'تم تسجيل عميل جديد: سارة أحمد',\n        time: 'منذ 30 دقيقة'\n      },\n      {\n        id: 4,\n        icon: 'fas fa-truck text-orange-500',\n        text: 'تم استلام شحنة من المورد الرئيسي',\n        time: 'منذ ساعة'\n      }\n    ])\n\n    // Methods\n    const toggleSidebar = () => {\n      sidebarCollapsed.value = !sidebarCollapsed.value\n    }\n\n    const setActiveSection = (section) => {\n      activeSection.value = section\n    }\n\n    const getSectionTitle = () => {\n      const titles = {\n        dashboard: 'لوحة التحكم',\n        products: 'إدارة المنتجات',\n        inventory: 'إدارة المخزون',\n        sales: 'تقارير المبيعات',\n        users: 'إدارة المستخدمين',\n        suppliers: 'إدارة الموردين'\n      }\n      return titles[activeSection.value] || 'لوحة التحكم'\n    }\n\n    const getSectionSubtitle = () => {\n      const subtitles = {\n        dashboard: 'نظرة عامة على أداء المتجر',\n        products: 'إضافة وتعديل وحذف المنتجات',\n        inventory: 'متابعة المخزون والكميات',\n        sales: 'تحليل المبيعات والأرباح',\n        users: 'إدارة حسابات المستخدمين',\n        suppliers: 'إدارة بيانات الموردين'\n      }\n      return subtitles[activeSection.value] || ''\n    }\n\n    const getSectionIcon = () => {\n      const icons = {\n        dashboard: 'fas fa-tachometer-alt',\n        products: 'fas fa-box',\n        inventory: 'fas fa-warehouse',\n        sales: 'fas fa-chart-line',\n        users: 'fas fa-users',\n        suppliers: 'fas fa-truck'\n      }\n      return icons[activeSection.value] || 'fas fa-tachometer-alt'\n    }\n\n    const handleLogout = () => {\n      authStore.logout()\n      router.push('/login')\n    }\n\n    // Lifecycle\n    onMounted(() => {\n      authStore.initializeAuth()\n    })\n\n    return {\n      sidebarCollapsed,\n      activeSection,\n      dashboardStatsArray,\n      recentActivities,\n      authStore,\n      toggleSidebar,\n      setActiveSection,\n      getSectionTitle,\n      getSectionSubtitle,\n      getSectionIcon,\n      handleLogout\n    }\n  }\n}\n</script>\n\n<style scoped>\n.dashboard-container {\n  display: flex;\n  min-height: 100vh;\n  background-color: #f8fafc;\n  font-family: 'Cairo', sans-serif;\n  direction: rtl;\n}\n\n/* ===== Main Content Styles ===== */\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.main-header {\n  background: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.header-left h1 {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n}\n\n.header-subtitle {\n  color: #64748b;\n  margin: 0;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.user-name {\n  font-weight: 500;\n  color: #334155;\n}\n\n.user-avatar {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n/* ===== Content Area Styles ===== */\n.content-area {\n  flex: 1;\n  padding: 2rem;\n  overflow-y: auto;\n}\n\n.dashboard-overview {\n  max-width: 1200px;\n}\n\n/* ===== Responsive Design ===== */\n@media (max-width: 768px) {\n  .main-header {\n    padding: 1rem;\n  }\n\n  .content-area {\n    padding: 1rem;\n  }\n}\n\n/* ===== Color Utilities ===== */\n.text-blue-500 { color: #3b82f6; }\n.text-green-500 { color: #10b981; }\n.text-purple-500 { color: #8b5cf6; }\n.text-orange-500 { color: #f59e0b; }\n</style>\n\n"], "mappings": ";AAiDA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,YAAW,QAAS,gBAAe;AAC5C,OAAOC,OAAM,MAAO,2BAA0B;AAC9C,OAAOC,cAAa,MAAO,kCAAiC;AAC5D,OAAOC,gBAAe,MAAO,oCAAmC;AAChE,OAAOC,kBAAiB,MAAO,sCAAqC;AAEpE,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVL,OAAO;IACPC,cAAc;IACdC,gBAAgB;IAChBC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIT,SAAS,CAAC;IACzB,MAAMU,SAAQ,GAAIT,YAAY,CAAC;;IAE/B;IACA,MAAMU,gBAAe,GAAIf,GAAG,CAAC,KAAK;IAClC,MAAMgB,aAAY,GAAIhB,GAAG,CAAC,WAAW;IAErC,MAAMiB,cAAa,GAAIhB,QAAQ,CAAC;MAC9BiB,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,MAAM;MAClBC,eAAe,EAAE,GAAG;MACpBC,cAAc,EAAE;IAClB,CAAC;IAED,MAAMC,mBAAkB,GAAIpB,QAAQ,CAAC,MAAM,CACzC;MACEqB,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAER,cAAc,CAACC,aAAa;MACnCQ,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,0BAA0B;MAChCC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAER,cAAc,CAACE,UAAU;MAChCO,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,kCAAkC;MACxCC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAER,cAAc,CAACG,eAAe;MACrCM,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,8BAA8B;MACpCC,MAAM,EAAE;IACV,CAAC,EACD;MACEL,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,gBAAgB;MACvBC,KAAK,EAAER,cAAc,CAACI,cAAc;MACpCK,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,kCAAkC;MACxCC,MAAM,EAAE,CAAC;IACX,EACD;IAED,MAAMC,gBAAe,GAAI7B,GAAG,CAAC,CAC3B;MACEuB,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,mCAAmC;MACzCG,IAAI,EAAE,mCAAmC;MACzCC,IAAI,EAAE;IACR,CAAC,EACD;MACER,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,oCAAoC;MAC1CG,IAAI,EAAE,8BAA8B;MACpCC,IAAI,EAAE;IACR,CAAC,EACD;MACER,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,kCAAkC;MACxCG,IAAI,EAAE,+BAA+B;MACrCC,IAAI,EAAE;IACR,CAAC,EACD;MACER,EAAE,EAAE,CAAC;MACLI,IAAI,EAAE,8BAA8B;MACpCG,IAAI,EAAE,kCAAkC;MACxCC,IAAI,EAAE;IACR,EACD;;IAED;IACA,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1BjB,gBAAgB,CAACU,KAAI,GAAI,CAACV,gBAAgB,CAACU,KAAI;IACjD;IAEA,MAAMQ,gBAAe,GAAKC,OAAO,IAAK;MACpClB,aAAa,CAACS,KAAI,GAAIS,OAAM;IAC9B;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMC,MAAK,GAAI;QACbC,SAAS,EAAE,aAAa;QACxBC,QAAQ,EAAE,gBAAgB;QAC1BC,SAAS,EAAE,eAAe;QAC1BC,KAAK,EAAE,iBAAiB;QACxBC,KAAK,EAAE,kBAAkB;QACzBC,SAAS,EAAE;MACb;MACA,OAAON,MAAM,CAACpB,aAAa,CAACS,KAAK,KAAK,aAAY;IACpD;IAEA,MAAMkB,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,SAAQ,GAAI;QAChBP,SAAS,EAAE,2BAA2B;QACtCC,QAAQ,EAAE,4BAA4B;QACtCC,SAAS,EAAE,yBAAyB;QACpCC,KAAK,EAAE,yBAAyB;QAChCC,KAAK,EAAE,yBAAyB;QAChCC,SAAS,EAAE;MACb;MACA,OAAOE,SAAS,CAAC5B,aAAa,CAACS,KAAK,KAAK,EAAC;IAC5C;IAEA,MAAMoB,cAAa,GAAIA,CAAA,KAAM;MAC3B,MAAMC,KAAI,GAAI;QACZT,SAAS,EAAE,uBAAuB;QAClCC,QAAQ,EAAE,YAAY;QACtBC,SAAS,EAAE,kBAAkB;QAC7BC,KAAK,EAAE,mBAAmB;QAC1BC,KAAK,EAAE,cAAc;QACrBC,SAAS,EAAE;MACb;MACA,OAAOI,KAAK,CAAC9B,aAAa,CAACS,KAAK,KAAK,uBAAsB;IAC7D;IAEA,MAAMsB,YAAW,GAAIA,CAAA,KAAM;MACzBjC,SAAS,CAACkC,MAAM,CAAC;MACjBnC,MAAM,CAACoC,IAAI,CAAC,QAAQ;IACtB;;IAEA;IACA9C,SAAS,CAAC,MAAM;MACdW,SAAS,CAACoC,cAAc,CAAC;IAC3B,CAAC;IAED,OAAO;MACLnC,gBAAgB;MAChBC,aAAa;MACbM,mBAAmB;MACnBO,gBAAgB;MAChBf,SAAS;MACTkB,aAAa;MACbC,gBAAgB;MAChBE,eAAe;MACfQ,kBAAkB;MAClBE,cAAc;MACdE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}