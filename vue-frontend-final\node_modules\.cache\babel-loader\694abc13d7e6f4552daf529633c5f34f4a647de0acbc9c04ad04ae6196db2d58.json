{"ast": null, "code": "export default {\n  name: 'DashboardStats',\n  props: {\n    stats: {\n      type: Array,\n      required: true\n    }\n  },\n  methods: {\n    formatValue(value, type) {\n      if (type === 'currency') {\n        return new Intl.NumberFormat('ar-SA', {\n          style: 'currency',\n          currency: 'SAR'\n        }).format(value);\n      }\n      return new Intl.NumberFormat('ar-SA').format(value);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "stats", "type", "Array", "required", "methods", "formatValue", "value", "Intl", "NumberFormat", "style", "currency", "format"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\DashboardStats.vue"], "sourcesContent": ["<template>\n  <div class=\"stats-grid\">\n    <div v-for=\"stat in stats\" :key=\"stat.id\" class=\"stat-card card-hover\">\n      <div class=\"stat-icon\">\n        <i :class=\"stat.icon\"></i>\n      </div>\n      <div class=\"stat-content\">\n        <h3>{{ stat.title }}</h3>\n        <p class=\"stat-number\">{{ formatValue(stat.value, stat.type) }}</p>\n        <span :class=\"['stat-change', stat.change >= 0 ? 'positive' : 'negative']\">\n          {{ stat.change >= 0 ? '+' : '' }}{{ stat.change }}% من الشهر الماضي\n        </span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DashboardStats',\n  props: {\n    stats: {\n      type: Array,\n      required: true\n    }\n  },\n  methods: {\n    formatValue(value, type) {\n      if (type === 'currency') {\n        return new Intl.NumberFormat('ar-SA', {\n          style: 'currency',\n          currency: 'SAR'\n        }).format(value)\n      }\n      return new Intl.NumberFormat('ar-SA').format(value)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.card-hover {\n  transition: all 0.3s ease;\n}\n\n.card-hover:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  background: rgba(14, 165, 233, 0.1);\n}\n\n.stat-content h3 {\n  font-size: 0.875rem;\n  color: #64748b;\n  margin: 0 0 8px 0;\n  font-weight: 500;\n}\n\n.stat-number {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n}\n\n.stat-change {\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.stat-change.positive {\n  color: #059669;\n}\n\n.stat-change.negative {\n  color: #dc2626;\n}\n</style>\n\n"], "mappings": "AAkBA,eAAe;EACbA,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,KAAK;MACXC,QAAQ,EAAE;IACZ;EACF,CAAC;EACDC,OAAO,EAAE;IACPC,WAAWA,CAACC,KAAK,EAAEL,IAAI,EAAE;MACvB,IAAIA,IAAG,KAAM,UAAU,EAAE;QACvB,OAAO,IAAIM,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;UACpCC,KAAK,EAAE,UAAU;UACjBC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK;MACjB;MACA,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACG,MAAM,CAACL,KAAK;IACpD;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}