{"ast": null, "code": "export default {\n  name: 'SectionPlaceholder',\n  props: {\n    icon: {\n      type: String,\n      required: true\n    },\n    title: {\n      type: String,\n      required: true\n    },\n    description: {\n      type: String,\n      default: 'هذا القسم قيد التطوير. سيتم إضافة المحتوى قريباً.'\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "icon", "type", "String", "required", "title", "description", "default"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\SectionPlaceholder.vue"], "sourcesContent": ["<template>\n  <div class=\"section-placeholder\">\n    <div class=\"placeholder-content\">\n      <i :class=\"icon\"></i>\n      <h2>{{ title }}</h2>\n      <p>{{ description }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SectionPlaceholder',\n  props: {\n    icon: {\n      type: String,\n      required: true\n    },\n    title: {\n      type: String,\n      required: true\n    },\n    description: {\n      type: String,\n      default: 'هذا القسم قيد التطوير. سيتم إضافة المحتوى قريباً.'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.section-placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n}\n\n.placeholder-content {\n  text-align: center;\n  color: #64748b;\n}\n\n.placeholder-content i {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n  color: #cbd5e1;\n}\n\n.placeholder-content h2 {\n  font-size: 1.5rem;\n  margin: 0 0 0.5rem 0;\n  color: #334155;\n}\n\n.placeholder-content p {\n  font-size: 1rem;\n  margin: 0;\n}\n</style>\n\n"], "mappings": "AAWA,eAAe;EACbA,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,KAAK,EAAE;MACLH,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDE,WAAW,EAAE;MACXJ,IAAI,EAAEC,MAAM;MACZI,OAAO,EAAE;IACX;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}