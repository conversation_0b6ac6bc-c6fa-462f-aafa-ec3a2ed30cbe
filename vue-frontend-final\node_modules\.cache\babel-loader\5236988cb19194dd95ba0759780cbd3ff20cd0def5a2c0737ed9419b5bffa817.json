{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"sidebar-header\"\n};\nconst _hoisted_2 = {\n  class: \"logo\"\n};\nconst _hoisted_3 = {\n  key: 0\n};\nconst _hoisted_4 = {\n  class: \"sidebar-nav\"\n};\nconst _hoisted_5 = [\"onClick\"];\nconst _hoisted_6 = {\n  key: 0\n};\nconst _hoisted_7 = {\n  class: \"sidebar-footer\"\n};\nconst _hoisted_8 = {\n  key: 0\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"aside\", {\n    class: _normalizeClass([\"sidebar\", {\n      'sidebar-collapsed': $props.collapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createElementVNode(\"i\", {\n    class: \"fas fa-store\"\n  }, null, -1 /* CACHED */)), !$props.collapsed ? (_openBlock(), _createElementBlock(\"span\", _hoisted_3, \"ElectroHub Pro\")) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$emit('toggle')),\n    class: \"sidebar-toggle\"\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass($props.collapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left')\n  }, null, 2 /* CLASS */)])]), _createElementVNode(\"nav\", _hoisted_4, [_createElementVNode(\"ul\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.menuItems, item => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: item.id\n    }, [_createElementVNode(\"a\", {\n      href: \"#\",\n      onClick: $event => _ctx.$emit('section-change', item.id),\n      class: _normalizeClass([{\n        active: $props.activeSection === item.id\n      }, \"sidebar-item\"])\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(item.icon)\n    }, null, 2 /* CLASS */), !$props.collapsed ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, _toDisplayString(item.title), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_5)]);\n  }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = $event => _ctx.$emit('logout')),\n    class: \"logout-btn\"\n  }, [_cache[3] || (_cache[3] = _createElementVNode(\"i\", {\n    class: \"fas fa-sign-out-alt\"\n  }, null, -1 /* CACHED */)), !$props.collapsed ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, \"تسجيل الخروج\")) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "collapsed", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "$event", "_ctx", "$emit", "_hoisted_4", "_Fragment", "_renderList", "$data", "menuItems", "item", "key", "id", "href", "active", "activeSection", "icon", "_hoisted_6", "_toDisplayString", "title", "_hoisted_7", "_hoisted_8"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\Sidebar.vue"], "sourcesContent": ["<template>\n  <aside class=\"sidebar\" :class=\"{ 'sidebar-collapsed': collapsed }\">\n    <div class=\"sidebar-header\">\n      <div class=\"logo\">\n        <i class=\"fas fa-store\"></i>\n        <span v-if=\"!collapsed\">ElectroHub Pro</span>\n      </div>\n      <button @click=\"$emit('toggle')\" class=\"sidebar-toggle\">\n        <i :class=\"collapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'\"></i>\n      </button>\n    </div>\n\n    <nav class=\"sidebar-nav\">\n      <ul>\n        <li v-for=\"item in menuItems\" :key=\"item.id\">\n          <a href=\"#\" @click=\"$emit('section-change', item.id)\" \n             :class=\"{ active: activeSection === item.id }\" class=\"sidebar-item\">\n            <i :class=\"item.icon\"></i>\n            <span v-if=\"!collapsed\">{{ item.title }}</span>\n          </a>\n        </li>\n      </ul>\n    </nav>\n\n    <div class=\"sidebar-footer\">\n      <button @click=\"$emit('logout')\" class=\"logout-btn\">\n        <i class=\"fas fa-sign-out-alt\"></i>\n        <span v-if=\"!collapsed\">تسجيل الخروج</span>\n      </button>\n    </div>\n  </aside>\n</template>\n\n<script>\nexport default {\n  name: 'AppSidebar',\n  props: {\n    collapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeSection: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n  emits: ['toggle', 'section-change', 'logout'],\n  data() {\n    return {\n      menuItems: [\n        {\n          id: 'dashboard',\n          title: 'لوحة التحكم',\n          icon: 'fas fa-tachometer-alt'\n        },\n        {\n          id: 'products',\n          title: 'المنتجات',\n          icon: 'fas fa-box'\n        },\n        {\n          id: 'inventory',\n          title: 'المخزون',\n          icon: 'fas fa-warehouse'\n        },\n        {\n          id: 'sales',\n          title: 'المبيعات',\n          icon: 'fas fa-chart-line'\n        },\n        {\n          id: 'users',\n          title: 'المستخدمين',\n          icon: 'fas fa-users'\n        },\n        {\n          id: 'suppliers',\n          title: 'الموردين',\n          icon: 'fas fa-truck'\n        }\n      ]\n    }\n  }\n}\n</script>\n\n<style scoped>\n.sidebar {\n  width: 280px;\n  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n  color: white;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n}\n\n.sidebar-collapsed {\n  width: 80px;\n}\n\n.sidebar-header {\n  padding: 1.5rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 1.25rem;\n  font-weight: 700;\n}\n\n.logo i {\n  font-size: 1.5rem;\n  color: #0ea5e9;\n}\n\n.sidebar-toggle {\n  background: none;\n  border: none;\n  color: white;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: background-color 0.3s ease;\n}\n\n.sidebar-toggle:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-nav {\n  flex: 1;\n  padding: 1rem 0;\n}\n\n.sidebar-nav ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.sidebar-nav li {\n  margin-bottom: 4px;\n}\n\n.sidebar-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px 1.5rem;\n  color: rgba(255, 255, 255, 0.8);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  cursor: pointer;\n}\n\n.sidebar-item:hover {\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  transform: translateX(-2px);\n  color: white;\n}\n\n.sidebar-item.active {\n  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);\n  color: white;\n}\n\n.sidebar-item i {\n  width: 20px;\n  text-align: center;\n}\n\n.sidebar-footer {\n  padding: 1.5rem;\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.logout-btn {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  width: 100%;\n  padding: 12px;\n  background: rgba(239, 68, 68, 0.1);\n  border: 1px solid rgba(239, 68, 68, 0.3);\n  color: #ef4444;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.logout-btn:hover {\n  background: rgba(239, 68, 68, 0.2);\n  transform: translateY(-1px);\n}\n</style>\n\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAM;;;;;EASdA,KAAK,EAAC;AAAa;;;;;;EAYnBA,KAAK,EAAC;AAAgB;;;;;uBAvB7BC,mBAAA,CA6BQ;IA7BDD,KAAK,EAAAE,eAAA,EAAC,SAAS;MAAA,qBAAgCC,MAAA,CAAAC;IAAS;MAC7DC,mBAAA,CAQM,OARNC,UAQM,GAPJD,mBAAA,CAGM,OAHNE,UAGM,G,0BAFJF,mBAAA,CAA4B;IAAzBL,KAAK,EAAC;EAAc,4B,CACVG,MAAA,CAAAC,SAAS,I,cAAtBH,mBAAA,CAA6C,QAAAO,UAAA,EAArB,gBAAc,K,qCAExCH,mBAAA,CAES;IAFAI,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,KAAK;IAAYb,KAAK,EAAC;MACrCK,mBAAA,CAA2E;IAAvEL,KAAK,EAAAE,eAAA,CAAEC,MAAA,CAAAC,SAAS;+BAIxBC,mBAAA,CAUM,OAVNS,UAUM,GATJT,mBAAA,CAQK,c,kBAPHJ,mBAAA,CAMKc,SAAA,QAAAC,WAAA,CANcC,KAAA,CAAAC,SAAS,EAAjBC,IAAI;yBAAflB,mBAAA,CAMK;MAN0BmB,GAAG,EAAED,IAAI,CAACE;QACvChB,mBAAA,CAII;MAJDiB,IAAI,EAAC,GAAG;MAAEb,OAAK,EAAAE,MAAA,IAAEC,IAAA,CAAAC,KAAK,mBAAmBM,IAAI,CAACE,EAAE;MAC/CrB,KAAK,EAAAE,eAAA;QAAAqB,MAAA,EAAYpB,MAAA,CAAAqB,aAAa,KAAKL,IAAI,CAACE;MAAE,GAAU,cAAc;QACpEhB,mBAAA,CAA0B;MAAtBL,KAAK,EAAAE,eAAA,CAAEiB,IAAI,CAACM,IAAI;8BACPtB,MAAA,CAAAC,SAAS,I,cAAtBH,mBAAA,CAA+C,QAAAyB,UAAA,EAAAC,gBAAA,CAApBR,IAAI,CAACS,KAAK,oB;sCAM7CvB,mBAAA,CAKM,OALNwB,UAKM,GAJJxB,mBAAA,CAGS;IAHAI,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,KAAK;IAAYb,KAAK,EAAC;gCACrCK,mBAAA,CAAmC;IAAhCL,KAAK,EAAC;EAAqB,4B,CACjBG,MAAA,CAAAC,SAAS,I,cAAtBH,mBAAA,CAA2C,QAAA6B,UAAA,EAAnB,cAAY,K", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}