{"ast": null, "code": "import { defineStore } from 'pinia';\nimport { ref, computed } from 'vue';\nexport const useAuthStore = defineStore('auth', () => {\n  // State\n  const user = ref(null);\n  const token = ref(localStorage.getItem('authToken') || null);\n  const isLoading = ref(false);\n  const error = ref(null);\n\n  // Getters\n  const isAuthenticated = computed(() => !!token.value);\n  const currentUser = computed(() => user.value);\n\n  // Actions\n  const login = async credentials => {\n    isLoading.value = true;\n    error.value = null;\n    try {\n      // محاكاة API call - يمكن استبدالها بـ API حقيقي\n      const response = await mockLogin(credentials);\n      if (response.success) {\n        token.value = response.token;\n        user.value = response.user;\n        localStorage.setItem('authToken', response.token);\n        localStorage.setItem('user', JSON.stringify(response.user));\n        return {\n          success: true,\n          user: response.user\n        };\n      } else {\n        throw new Error(response.message || 'فشل في تسجيل الدخول');\n      }\n    } catch (err) {\n      error.value = err.message;\n      throw err;\n    } finally {\n      isLoading.value = false;\n    }\n  };\n  const logout = () => {\n    token.value = null;\n    user.value = null;\n    localStorage.removeItem('authToken');\n    localStorage.removeItem('user');\n  };\n  const initializeAuth = () => {\n    const savedUser = localStorage.getItem('user');\n    if (savedUser && token.value) {\n      try {\n        user.value = JSON.parse(savedUser);\n      } catch (e) {\n        logout();\n      }\n    }\n  };\n\n  // Mock login function - محاكاة تسجيل الدخول\n  const mockLogin = async credentials => {\n    return new Promise(resolve => {\n      setTimeout(() => {\n        if (credentials.username === 'Ahmed' && credentials.password === 'Ahmed123!') {\n          resolve({\n            success: true,\n            token: 'mock-jwt-token-' + Date.now(),\n            user: {\n              id: 1,\n              username: 'Ahmed',\n              fullName: 'أحمد محمد',\n              email: '<EMAIL>',\n              role: 'admin'\n            }\n          });\n        } else {\n          resolve({\n            success: false,\n            message: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n          });\n        }\n      }, 1000);\n    });\n  };\n  return {\n    // State\n    user,\n    token,\n    isLoading,\n    error,\n    // Getters\n    isAuthenticated,\n    currentUser,\n    // Actions\n    login,\n    logout,\n    initializeAuth\n  };\n});", "map": {"version": 3, "names": ["defineStore", "ref", "computed", "useAuthStore", "user", "token", "localStorage", "getItem", "isLoading", "error", "isAuthenticated", "value", "currentUser", "login", "credentials", "response", "mockLogin", "success", "setItem", "JSON", "stringify", "Error", "message", "err", "logout", "removeItem", "initializeAuth", "savedUser", "parse", "e", "Promise", "resolve", "setTimeout", "username", "password", "Date", "now", "id", "fullName", "email", "role"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/new/vue-frontend-project/vue-frontend-final/src/stores/auth.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\n\nexport const useAuthStore = defineStore('auth', () => {\n  // State\n  const user = ref(null)\n  const token = ref(localStorage.getItem('authToken') || null)\n  const isLoading = ref(false)\n  const error = ref(null)\n\n  // Getters\n  const isAuthenticated = computed(() => !!token.value)\n  const currentUser = computed(() => user.value)\n\n  // Actions\n  const login = async (credentials) => {\n    isLoading.value = true\n    error.value = null\n    \n    try {\n      // محاكاة API call - يمكن استبدالها بـ API حقيقي\n      const response = await mockLogin(credentials)\n      \n      if (response.success) {\n        token.value = response.token\n        user.value = response.user\n        localStorage.setItem('authToken', response.token)\n        localStorage.setItem('user', JSON.stringify(response.user))\n        return { success: true, user: response.user }\n      } else {\n        throw new Error(response.message || 'فشل في تسجيل الدخول')\n      }\n    } catch (err) {\n      error.value = err.message\n      throw err\n    } finally {\n      isLoading.value = false\n    }\n  }\n\n  const logout = () => {\n    token.value = null\n    user.value = null\n    localStorage.removeItem('authToken')\n    localStorage.removeItem('user')\n  }\n\n  const initializeAuth = () => {\n    const savedUser = localStorage.getItem('user')\n    if (savedUser && token.value) {\n      try {\n        user.value = JSON.parse(savedUser)\n      } catch (e) {\n        logout()\n      }\n    }\n  }\n\n  // Mock login function - محاكاة تسجيل الدخول\n  const mockLogin = async (credentials) => {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        if (credentials.username === 'Ahmed' && credentials.password === 'Ahmed123!') {\n          resolve({\n            success: true,\n            token: 'mock-jwt-token-' + Date.now(),\n            user: {\n              id: 1,\n              username: 'Ahmed',\n              fullName: 'أحمد محمد',\n              email: '<EMAIL>',\n              role: 'admin'\n            }\n          })\n        } else {\n          resolve({\n            success: false,\n            message: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n          })\n        }\n      }, 1000)\n    })\n  }\n\n  return {\n    // State\n    user,\n    token,\n    isLoading,\n    error,\n    // Getters\n    isAuthenticated,\n    currentUser,\n    // Actions\n    login,\n    logout,\n    initializeAuth\n  }\n})\n\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AAEnC,OAAO,MAAMC,YAAY,GAAGH,WAAW,CAAC,MAAM,EAAE,MAAM;EACpD;EACA,MAAMI,IAAI,GAAGH,GAAG,CAAC,IAAI,CAAC;EACtB,MAAMI,KAAK,GAAGJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;EAC5D,MAAMC,SAAS,GAAGP,GAAG,CAAC,KAAK,CAAC;EAC5B,MAAMQ,KAAK,GAAGR,GAAG,CAAC,IAAI,CAAC;;EAEvB;EACA,MAAMS,eAAe,GAAGR,QAAQ,CAAC,MAAM,CAAC,CAACG,KAAK,CAACM,KAAK,CAAC;EACrD,MAAMC,WAAW,GAAGV,QAAQ,CAAC,MAAME,IAAI,CAACO,KAAK,CAAC;;EAE9C;EACA,MAAME,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnCN,SAAS,CAACG,KAAK,GAAG,IAAI;IACtBF,KAAK,CAACE,KAAK,GAAG,IAAI;IAElB,IAAI;MACF;MACA,MAAMI,QAAQ,GAAG,MAAMC,SAAS,CAACF,WAAW,CAAC;MAE7C,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpBZ,KAAK,CAACM,KAAK,GAAGI,QAAQ,CAACV,KAAK;QAC5BD,IAAI,CAACO,KAAK,GAAGI,QAAQ,CAACX,IAAI;QAC1BE,YAAY,CAACY,OAAO,CAAC,WAAW,EAAEH,QAAQ,CAACV,KAAK,CAAC;QACjDC,YAAY,CAACY,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACL,QAAQ,CAACX,IAAI,CAAC,CAAC;QAC3D,OAAO;UAAEa,OAAO,EAAE,IAAI;UAAEb,IAAI,EAAEW,QAAQ,CAACX;QAAK,CAAC;MAC/C,CAAC,MAAM;QACL,MAAM,IAAIiB,KAAK,CAACN,QAAQ,CAACO,OAAO,IAAI,qBAAqB,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZd,KAAK,CAACE,KAAK,GAAGY,GAAG,CAACD,OAAO;MACzB,MAAMC,GAAG;IACX,CAAC,SAAS;MACRf,SAAS,CAACG,KAAK,GAAG,KAAK;IACzB;EACF,CAAC;EAED,MAAMa,MAAM,GAAGA,CAAA,KAAM;IACnBnB,KAAK,CAACM,KAAK,GAAG,IAAI;IAClBP,IAAI,CAACO,KAAK,GAAG,IAAI;IACjBL,YAAY,CAACmB,UAAU,CAAC,WAAW,CAAC;IACpCnB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,SAAS,GAAGrB,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC9C,IAAIoB,SAAS,IAAItB,KAAK,CAACM,KAAK,EAAE;MAC5B,IAAI;QACFP,IAAI,CAACO,KAAK,GAAGQ,IAAI,CAACS,KAAK,CAACD,SAAS,CAAC;MACpC,CAAC,CAAC,OAAOE,CAAC,EAAE;QACVL,MAAM,CAAC,CAAC;MACV;IACF;EACF,CAAC;;EAED;EACA,MAAMR,SAAS,GAAG,MAAOF,WAAW,IAAK;IACvC,OAAO,IAAIgB,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAM;QACf,IAAIlB,WAAW,CAACmB,QAAQ,KAAK,OAAO,IAAInB,WAAW,CAACoB,QAAQ,KAAK,WAAW,EAAE;UAC5EH,OAAO,CAAC;YACNd,OAAO,EAAE,IAAI;YACbZ,KAAK,EAAE,iBAAiB,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC;YACrChC,IAAI,EAAE;cACJiC,EAAE,EAAE,CAAC;cACLJ,QAAQ,EAAE,OAAO;cACjBK,QAAQ,EAAE,WAAW;cACrBC,KAAK,EAAE,mBAAmB;cAC1BC,IAAI,EAAE;YACR;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACLT,OAAO,CAAC;YACNd,OAAO,EAAE,KAAK;YACdK,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,OAAO;IACL;IACAlB,IAAI;IACJC,KAAK;IACLG,SAAS;IACTC,KAAK;IACL;IACAC,eAAe;IACfE,WAAW;IACX;IACAC,KAAK;IACLW,MAAM;IACNE;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}