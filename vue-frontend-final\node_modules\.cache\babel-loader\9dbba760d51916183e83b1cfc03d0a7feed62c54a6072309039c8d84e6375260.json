{"ast": null, "code": "import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"section-placeholder\"\n};\nconst _hoisted_2 = {\n  class: \"placeholder-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"i\", {\n    class: _normalizeClass($props.icon)\n  }, null, 2 /* CLASS */), _createElementVNode(\"h2\", null, _toDisplayString($props.title), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString($props.description), 1 /* TEXT */)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_normalizeClass", "$props", "icon", "_toDisplayString", "title", "description"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\SectionPlaceholder.vue"], "sourcesContent": ["<template>\n  <div class=\"section-placeholder\">\n    <div class=\"placeholder-content\">\n      <i :class=\"icon\"></i>\n      <h2>{{ title }}</h2>\n      <p>{{ description }}</p>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SectionPlaceholder',\n  props: {\n    icon: {\n      type: String,\n      required: true\n    },\n    title: {\n      type: String,\n      required: true\n    },\n    description: {\n      type: String,\n      default: 'هذا القسم قيد التطوير. سيتم إضافة المحتوى قريباً.'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.section-placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n}\n\n.placeholder-content {\n  text-align: center;\n  color: #64748b;\n}\n\n.placeholder-content i {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n  color: #cbd5e1;\n}\n\n.placeholder-content h2 {\n  font-size: 1.5rem;\n  margin: 0 0 0.5rem 0;\n  color: #334155;\n}\n\n.placeholder-content p {\n  font-size: 1rem;\n  margin: 0;\n}\n</style>\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAqB;;uBADlCC,mBAAA,CAMM,OANNC,UAMM,GALJC,mBAAA,CAIM,OAJNC,UAIM,GAHJD,mBAAA,CAAqB;IAAjBH,KAAK,EAAAK,eAAA,CAAEC,MAAA,CAAAC,IAAI;2BACfJ,mBAAA,CAAoB,YAAAK,gBAAA,CAAbF,MAAA,CAAAG,KAAK,kBACZN,mBAAA,CAAwB,WAAAK,gBAAA,CAAlBF,MAAA,CAAAI,WAAW,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}