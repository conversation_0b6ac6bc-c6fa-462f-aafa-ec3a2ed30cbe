{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_2 = {\n  class: \"stat-icon\"\n};\nconst _hoisted_3 = {\n  class: \"stat-content\"\n};\nconst _hoisted_4 = {\n  class: \"stat-number\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.stats, stat => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: stat.id,\n      class: \"stat-card card-hover\"\n    }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"i\", {\n      class: _normalizeClass(stat.icon)\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h3\", null, _toDisplayString(stat.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_4, _toDisplayString($options.formatValue(stat.value, stat.type)), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass(['stat-change', stat.change >= 0 ? 'positive' : 'negative'])\n    }, _toDisplayString(stat.change >= 0 ? '+' : '') + _toDisplayString(stat.change) + \"% من الشهر الماضي \", 3 /* TEXT, CLASS */)])]);\n  }), 128 /* KEYED_FRAGMENT */))]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_Fragment", "_renderList", "$props", "stats", "stat", "key", "id", "_createElementVNode", "_hoisted_2", "_normalizeClass", "icon", "_hoisted_3", "_toDisplayString", "title", "_hoisted_4", "$options", "formatValue", "value", "type", "change"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\new\\vue-frontend-project\\vue-frontend-final\\src\\components\\DashboardStats.vue"], "sourcesContent": ["<template>\n  <div class=\"stats-grid\">\n    <div v-for=\"stat in stats\" :key=\"stat.id\" class=\"stat-card card-hover\">\n      <div class=\"stat-icon\">\n        <i :class=\"stat.icon\"></i>\n      </div>\n      <div class=\"stat-content\">\n        <h3>{{ stat.title }}</h3>\n        <p class=\"stat-number\">{{ formatValue(stat.value, stat.type) }}</p>\n        <span :class=\"['stat-change', stat.change >= 0 ? 'positive' : 'negative']\">\n          {{ stat.change >= 0 ? '+' : '' }}{{ stat.change }}% من الشهر الماضي\n        </span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'DashboardStats',\n  props: {\n    stats: {\n      type: Array,\n      required: true\n    }\n  },\n  methods: {\n    formatValue(value, type) {\n      if (type === 'currency') {\n        return new Intl.NumberFormat('ar-SA', {\n          style: 'currency',\n          currency: 'SAR'\n        }).format(value)\n      }\n      return new Intl.NumberFormat('ar-SA').format(value)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.card-hover {\n  transition: all 0.3s ease;\n}\n\n.card-hover:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  background: rgba(14, 165, 233, 0.1);\n}\n\n.stat-content h3 {\n  font-size: 0.875rem;\n  color: #64748b;\n  margin: 0 0 8px 0;\n  font-weight: 500;\n}\n\n.stat-number {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 4px 0;\n}\n\n.stat-change {\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.stat-change.positive {\n  color: #059669;\n}\n\n.stat-change.negative {\n  color: #dc2626;\n}\n</style>\n\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAW;;EAGjBA,KAAK,EAAC;AAAc;;EAEpBA,KAAK,EAAC;AAAa;;uBAP5BC,mBAAA,CAaM,OAbNC,UAaM,I,kBAZJD,mBAAA,CAWME,SAAA,QAAAC,WAAA,CAXcC,MAAA,CAAAC,KAAK,EAAbC,IAAI;yBAAhBN,mBAAA,CAWM;MAXsBO,GAAG,EAAED,IAAI,CAACE,EAAE;MAAET,KAAK,EAAC;QAC9CU,mBAAA,CAEM,OAFNC,UAEM,GADJD,mBAAA,CAA0B;MAAtBV,KAAK,EAAAY,eAAA,CAAEL,IAAI,CAACM,IAAI;+BAEtBH,mBAAA,CAMM,OANNI,UAMM,GALJJ,mBAAA,CAAyB,YAAAK,gBAAA,CAAlBR,IAAI,CAACS,KAAK,kBACjBN,mBAAA,CAAmE,KAAnEO,UAAmE,EAAAF,gBAAA,CAAzCG,QAAA,CAAAC,WAAW,CAACZ,IAAI,CAACa,KAAK,EAAEb,IAAI,CAACc,IAAI,mBAC3DX,mBAAA,CAEO;MAFAV,KAAK,EAAAY,eAAA,iBAAkBL,IAAI,CAACe,MAAM;wBACpCf,IAAI,CAACe,MAAM,oBAAAP,gBAAA,CAAsBR,IAAI,CAACe,MAAM,IAAG,oBACpD,uB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}